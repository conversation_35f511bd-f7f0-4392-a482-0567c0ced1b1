#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ASR管理器
统一管理单语种和多语种的ASR模型, 支持动态切换和实时语种识别
"""

import os
import threading
from typing import Any, Dict, List, Optional

import onnxruntime as ort

from modules.config import config_manager
from modules.decoder import load_onnx
from modules.logger import logger
from modules.onnx_session_pool import get_global_session_pool
from modules.symbol_table import SymbolTable


class ASRManager:
    """统一的ASR管理器, 负责管理各个语种的ASR模型"""

    def __init__(self, global_config):
        """
        初始化ASR管理器

        Args:
            global_config: 全局配置对象
        """
        self.global_config = global_config
        self.supported_languages = global_config.supported_languages
        self.loaded_models = {}  # 存储已加载的模型信息 {lang_code: {sessions, metadatas, config, symbol_table, feat_pipe}}

        # 线程安全锁：为每个语种使用独立的锁
        self.model_locks = {}  # {lang_code: threading.RLock()}
        self.global_lock = threading.RLock()  # 用于保护 model_locks 字典本身
        self.session_pool = get_global_session_pool()

        logger.info("初始化统一ASR管理器, 支持语种: {}".format(self.supported_languages))

    def load_models(self, lang_codes) -> bool:
        """
        根据lang_code加载模型
        Args:
            lang_codes: 语种代码, 可以是字符串或列表
                       - 'multi': 加载所有支持的语种
                       - 'zh': 加载单个语种
                       - ['zh', 'en']: 加载指定的多个语种
        Returns:
            bool: 是否加载成功
        """
        # 处理lang_codes参数
        if isinstance(lang_codes, str):
            if lang_codes == "multi":
                target_languages = self.supported_languages
                logger.info("多语种模式：预加载所有支持的语种模型")
            else:
                target_languages = [lang_codes]
                logger.info(f"单语种模式：加载语种 {lang_codes}")
        else:
            target_languages = lang_codes
            logger.info(f"指定语种模式：加载语种 {target_languages}")

        # 加载每个语种的模型
        success_count = 0
        for lang_code in target_languages:
            if self._load_single_language(lang_code):
                success_count += 1
            else:
                logger.warning(f"加载语种 {lang_code} 失败")

        logger.info(f"模型加载完成：成功 {success_count}/{len(target_languages)} 个语种")
        return success_count > 0

    def _load_single_language(self, lang_code: str) -> bool:
        """
        加载指定语种的ASR模型

        Args:
            lang_code: 语种代码

        Returns:
            bool: 是否加载成功
        """
        # 双重检查锁定模式：先检查是否已加载（无锁）
        if lang_code in self.loaded_models:
            logger.debug(f"语种 {lang_code} 的模型已加载（快速路径）")
            return True

        # 获取或创建语种特定的锁
        with self.global_lock:
            if lang_code not in self.model_locks:
                self.model_locks[lang_code] = threading.RLock()
            lang_lock = self.model_locks[lang_code]

        # 使用语种特定的锁（允许不同语种并行加载）
        with lang_lock:
            # 再次检查（可能在等待锁期间其他线程已加载）
            if lang_code in self.loaded_models:
                logger.debug(f"语种 {lang_code} 的模型已加载（锁内检查）")
                return True

            try:
                # 获取语种配置
                lang_config = config_manager.load_language_config(lang_code)
                if not lang_config:
                    logger.error(f"无法加载语种 {lang_code} 的配置")
                    return False

                # 验证模型路径
                if not os.path.exists(lang_config.onnx_dir):
                    logger.error(f"语种 {lang_code} 的模型路径不存在: {lang_config.onnx_dir}")
                    return False

                # 立即注册模型到会话池
                metadatas = self._register_models_to_session_pool(lang_code, lang_config)
                logger.info(f"语种 {lang_code} 模型已注册到会话池")

                # 使用新的配置构建方法
                model_config = lang_config.build_model_config(metadatas, self.global_config.decode)

                # 创建词表
                symbol_table = SymbolTable(
                    lang_config.dict_path,
                    lang_code,
                    lang_config.map_path,
                    lang_config.lower,
                    lang_config.remove_spm
                )

                # 创建特征管道
                from modules.feature import FeaturePipeline
                feat_pipe = FeaturePipeline(model_config['feat_configs'])

                # 保存加载的模型信息（不再存储sessions）
                self.loaded_models[lang_code] = {
                    'metadatas': metadatas,
                    'config': model_config,
                    'symbol_table': symbol_table,
                    'lang_config': lang_config,
                    'feat_pipe': feat_pipe
                }
                return True

            except Exception as e:
                logger.error(f"加载语种 {lang_code} 模型失败: {e}")
                return False

    def _register_models_to_session_pool(self, lang_code: str, lang_config):
        """
        将模型注册到会话池

        Args:
            lang_code: 语种代码
            lang_config: 语种配置
        """
        try:
            metadatas = {}
            # 为每个模型类型注册会话工厂
            for model_type in ['encoder', 'ctc']:
                model_name = f"{lang_code}_{model_type}"

                # 创建会话工厂函数
                def create_session_factory(model_type, lang_config):
                    def factory():
                        mode = self.global_config.decode.mode
                        return load_onnx(
                                        onnx_dir=lang_config.onnx_dir,
                                        onnx_name=model_type,
                                        fp16=lang_config.fp16,
                                        quant=lang_config.quant,
                                        mode=mode,
                                        device=lang_config.device,
                                        device_id=lang_config.device_id
                                    )
                    return factory

                # 注册到会话池
                self.session_pool.register_model(
                    model_name,
                    create_session_factory(model_type, lang_config)
                )

                metadatas[model_type] = self.session_pool.session_pools[model_name][0].metadata
            
            return metadatas

        except Exception as e:
            logger.error(f"注册模型到会话池失败: {e}")
            return None


    def switch_to_language(self, lang_code: str) -> bool:
        """
        切换到指定语种的模型

        Args:
            lang_code: 目标语种代码

        Returns:
            bool: 是否切换成功
        """
        if lang_code not in self.supported_languages:
            logger.warning(f"不支持的语种: {lang_code}")
            return False

        # 快速检查：如果模型已加载，直接返回（无需锁）
        if lang_code in self.loaded_models:
            logger.debug(f"会话池语种 {lang_code} 已准备就绪（已加载）")
            return True

        # 只有在模型未加载时才需要锁
        if not self._load_single_language(lang_code):
            return False

        logger.info(f"会话池语种 {lang_code} 已准备就绪")
        return True

    def get_symbol_table(self, lang_code: str) -> Optional[SymbolTable]:
        """获取指定语种的词表"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['symbol_table']
        return None

    def get_config(self, lang_code: str) -> Optional[Dict]:
        """获取指定语种的配置"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['config']
        return None

    def get_lang_config(self, lang_code: str) -> Optional[Any]:
        """获取指定语种的语言配置"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['lang_config']
        return None

    def get_feat_pipe(self, lang_code: str) -> Optional[Any]:
        """获取指定语种的特征管道"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['feat_pipe']
        return None
    
    def is_language_loaded(self, lang_code: str) -> bool:
        """检查指定语种是否已加载"""
        return lang_code in self.loaded_models
    
    def get_loaded_languages(self) -> List[str]:
        """获取已加载的语种列表"""
        return list(self.loaded_models.keys())

    def unload_language(self, lang_code: str):
        """卸载指定语种的模型"""
        if lang_code in self.loaded_models:
            del self.loaded_models[lang_code]
            logger.info(f"已卸载语种 {lang_code} 的模型")

    def cleanup(self):
        """清理所有资源"""
        logger.info("清理ASR管理器资源")
        self.loaded_models.clear()


