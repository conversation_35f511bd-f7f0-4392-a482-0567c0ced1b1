2025-07-09 08:25:38.952 | INFO  | modules.config:init_logger :593 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-09 08:25:38.966 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 8081
2025-07-09 08:25:38.966 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-09 08:25:38.967 | INFO  | modules.config:init_monitoring:624 - 监控系统初始化成功
2025-07-09 08:25:38.969 | INFO  | modules.onnx_session_pool:__init__    :60 - ONNX会话池已启用, 每个模型最大会话数: 4
2025-07-09 08:25:38.970 | INFO  | modules.config:init_session_pool:649 - ONNX会话池初始化成功
2025-07-09 08:25:38.970 | INFO  | modules.config:init_all_modules:667 - 所有模块初始化完成
2025-07-09 08:25:38.970 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-09 08:25:38.970 | INFO  | server :lifespan    :88 - 启动多语种ASR服务模式: 预加载所有支持的语种模型, 自动识别语种并切换
2025-07-09 08:25:38.970 | INFO  | modules.asr_manager:load_models :56 - 多语种模式：预加载所有支持的语种模型
2025-07-09 08:25:38.971 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-09 08:25:38.971 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:40.414 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-09 08:25:40.415 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:40.435 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 zh 模型已注册到会话池
2025-07-09 08:25:40.441 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': '/ws/MODELS/online_onnx_zh/hotwords.txt', 'context_graph_score': 40, 'output_size': 512, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5538}
2025-07-09 08:25:40.441 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-09 08:25:40.448 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 08:25:40.448 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_en, fp16: False, quant: False, device: cpu
2025-07-09 08:25:40.448 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:42.363 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_en, fp16: False, quant: False, device: cpu
2025-07-09 08:25:42.363 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:42.385 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 en 模型已注册到会话池
2025-07-09 08:25:42.389 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 101, 'subsampling_rate': 6, 'right_context': 10, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 512, 'num_blocks': 16, 'cnn_module_kernel': 31, 'head': 8, 'feature_size': 80, 'vocab_size': 5999}
2025-07-09 08:25:42.389 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_en/units.txt
2025-07-09 08:25:42.397 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 08:25:42.397 | DEBUG | modules.symbol_table:__init__    :27 - 启用后处理: 去占位下划线
2025-07-09 08:25:42.397 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ru, fp16: False, quant: True, device: cpu
2025-07-09 08:25:42.397 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:42.810 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ru, fp16: False, quant: True, device: cpu
2025-07-09 08:25:42.811 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:42.818 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 ru 模型已注册到会话池
2025-07-09 08:25:42.822 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5107}
2025-07-09 08:25:42.822 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_ru/units.txt
2025-07-09 08:25:42.828 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 08:25:42.829 | DEBUG | modules.symbol_table:__init__    :27 - 启用后处理: 去占位下划线
2025-07-09 08:25:42.829 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kk, fp16: False, quant: True, device: cpu
2025-07-09 08:25:42.829 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:43.338 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kk, fp16: False, quant: True, device: cpu
2025-07-09 08:25:43.339 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:43.346 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 kk 模型已注册到会话池
2025-07-09 08:25:43.349 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 8, 'head': 4, 'feature_size': 80, 'vocab_size': 5075}
2025-07-09 08:25:43.349 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_kk/units.txt
2025-07-09 08:25:43.356 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 08:25:43.356 | DEBUG | modules.symbol_table:__init__    :27 - 启用后处理: 去占位下划线
2025-07-09 08:25:43.356 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kkin, fp16: False, quant: True, device: cpu
2025-07-09 08:25:43.357 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:43.765 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kkin, fp16: False, quant: True, device: cpu
2025-07-09 08:25:43.765 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:43.773 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 kkin 模型已注册到会话池
2025-07-09 08:25:43.777 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 4, 'feature_size': 80, 'vocab_size': 5006}
2025-07-09 08:25:43.777 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_kkin/units.txt
2025-07-09 08:25:43.784 | INFO  | modules.symbol_table:__init__    :15 - 加载映射表: /ws/MODELS/online_onnx_kkin/map_kkin2lat.txt
2025-07-09 08:25:43.784 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ug, fp16: False, quant: True, device: cpu
2025-07-09 08:25:43.784 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:44.195 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ug, fp16: False, quant: True, device: cpu
2025-07-09 08:25:44.195 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:44.203 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 ug 模型已注册到会话池
2025-07-09 08:25:44.207 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 4, 'feature_size': 80, 'vocab_size': 5002}
2025-07-09 08:25:44.207 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_ug/units.txt
2025-07-09 08:25:44.213 | INFO  | modules.symbol_table:__init__    :15 - 加载映射表: /ws/MODELS/online_onnx_ug/map_uyg2lat.txt
2025-07-09 08:25:44.213 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 6/6 个语种
2025-07-09 08:25:44.215 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 08:25:44.281 | DEBUG | modules.lid_manager:_load_lid_model:94 - LID模型加载成功: /ws/MODELS/lid_model/lid.onnx
2025-07-09 08:25:44.282 | DEBUG | modules.lid_manager:_load_lid_model:104 - LID语种字典加载成功: /ws/MODELS/lid_model/spk2id.json
2025-07-09 08:25:44.282 | DEBUG | modules.lid_manager:_load_lid_model:113 - 应用全局CMVN: /ws/MODELS/lid_model/global_cmvn
2025-07-09 08:25:44.282 | INFO  | server :lifespan    :106 - LID管理器初始化成功: /ws/MODELS/lid_model/lid.onnx
2025-07-09 08:25:44.282 | INFO  | server :lifespan    :118 - Server start, init manager, LID_MANAGER, ASR_MANAGER
2025-07-09 08:25:54.306 | INFO  | server :websocket_endpoint:231 - client_id:111 - 开始初始化连接
2025-07-09 08:25:54.307 | DEBUG | modules.connect:connect     :181 - client_id:111 - 开始接受WebSocket连接
2025-07-09 08:25:54.307 | DEBUG | modules.connect:connect     :183 - client_id:111 - WebSocket连接已接受
2025-07-09 08:25:54.308 | DEBUG | modules.connect:connect     :187 - client_id:111 - WebSocket连接已存储到active_connection
2025-07-09 08:25:54.308 | INFO  | server :websocket_endpoint:237 - client_id:111 - 连接初始化完成，当前活跃连接数: 1
2025-07-09 08:25:54.308 | DEBUG | server :websocket_endpoint:238 - client_id:111 - 连接状态: True
2025-07-09 08:25:54.308 | DEBUG | server :websocket_endpoint:239 - client_id:111 - WebSocket状态: WebSocketState.CONNECTED
2025-07-09 08:25:54.309 | DEBUG | server :websocket_endpoint:247 - client_id:111 - 启动消息接收协程
2025-07-09 08:25:54.309 | INFO  | server :websocket_endpoint:231 - client_id:555 - 开始初始化连接
2025-07-09 08:25:54.310 | DEBUG | modules.connect:connect     :181 - client_id:555 - 开始接受WebSocket连接
2025-07-09 08:25:54.310 | DEBUG | modules.connect:connect     :183 - client_id:555 - WebSocket连接已接受
2025-07-09 08:25:54.310 | DEBUG | modules.connect:connect     :187 - client_id:555 - WebSocket连接已存储到active_connection
2025-07-09 08:25:54.311 | INFO  | server :websocket_endpoint:237 - client_id:555 - 连接初始化完成，当前活跃连接数: 2
2025-07-09 08:25:54.311 | DEBUG | server :websocket_endpoint:238 - client_id:555 - 连接状态: True
2025-07-09 08:25:54.311 | DEBUG | server :websocket_endpoint:239 - client_id:555 - WebSocket状态: WebSocketState.CONNECTED
2025-07-09 08:25:54.311 | DEBUG | server :websocket_endpoint:247 - client_id:555 - 启动消息接收协程
2025-07-09 08:25:54.313 | DEBUG | server :receive     :297 - client_id:111 - 进入消息接收循环
2025-07-09 08:25:54.313 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:54.314 | DEBUG | server :receive     :297 - client_id:555 - 进入消息接收循环
2025-07-09 08:25:54.314 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:54.489 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:54.489 | INFO  | modules.connect:on_check    :446 - client_id:111 - 设置自定义分隔符: ", "
2025-07-09 08:25:54.490 | INFO  | modules.connect:_handle_language_options:158 - client_id:111 - 客户端指定语种: en, 跳过LID
2025-07-09 08:25:54.490 | INFO  | modules.connect:_init_decoder:134 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-09 08:25:54.507 | INFO  | modules.connect:_init_decoder:141 - client_id:111 - 解码器初始化完成, 分隔符: ", "
2025-07-09 08:25:54.513 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 08:25:54.514 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:54.514 | DEBUG | modules.connect:on_decode   :934 - client_id:111 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-09 08:25:54.514 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 08:25:54.515 | INFO  | modules.connect:_switch_asr_model:849 - client_id:111 - 重新创建解码器以使用新语种配置
2025-07-09 08:25:54.515 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:25:54.515 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:25:54.515 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 08:25:54.516 | INFO  | modules.connect:_switch_asr_model:858 - client_id:111 - 成功切换到语种: en
2025-07-09 08:25:54.525 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:54.525 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:54.658 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:54.659 | INFO  | modules.connect:on_check    :446 - client_id:555 - 设置自定义分隔符: ", "
2025-07-09 08:25:54.659 | INFO  | modules.connect:_handle_language_options:158 - client_id:555 - 客户端指定语种: en, 跳过LID
2025-07-09 08:25:54.659 | INFO  | modules.connect:_init_decoder:134 - client_id:555 - 初始化解码器, 使用默认语种: zh
2025-07-09 08:25:54.663 | INFO  | modules.connect:_init_decoder:141 - client_id:555 - 解码器初始化完成, 分隔符: ", "
2025-07-09 08:25:54.670 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 08:25:54.671 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:54.671 | DEBUG | modules.connect:on_decode   :934 - client_id:555 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-09 08:25:54.672 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 08:25:54.672 | INFO  | modules.connect:_switch_asr_model:849 - client_id:555 - 重新创建解码器以使用新语种配置
2025-07-09 08:25:54.673 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:25:54.673 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:25:54.674 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 08:25:54.675 | INFO  | modules.connect:_switch_asr_model:858 - client_id:555 - 成功切换到语种: en
2025-07-09 08:25:54.682 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:54.682 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:54.897 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:54.902 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 08:25:54.903 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:54.903 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-09 08:25:54.904 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:54.904 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:54.904 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:55.067 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:55.074 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 08:25:55.074 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:55.075 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-09 08:25:55.075 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:55.075 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:55.076 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:55.306 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:55.322 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 08:25:55.323 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:55.323 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-09 08:25:55.323 | DEBUG | modules.decoder:decode      :501 - client_id:111 - 第0个chunk, 原始帧: 0~101,  torch.Size([1, 101, 80])
2025-07-09 08:25:55.324 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.418 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.419 | DEBUG | modules.decoder:decode_chunk:466 - client_id:111 - encoder 累计时点: 16
2025-07-09 08:25:55.419 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - ctc 搜索起始时点: 0 encoder_lens: tensor([16])
2025-07-09 08:25:55.419 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.420 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.437 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:55.437 | DEBUG | modules.decoder:search      :358 - ctc token&time [(5343, 12)]
2025-07-09 08:25:55.437 | DEBUG | modules.decoder:decode_chunk:481 - client_id:111 - ctc 累计搜索结果: "the"
2025-07-09 08:25:55.437 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:55.438 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the...
2025-07-09 08:25:55.438 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "the"
2025-07-09 08:25:55.438 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:55.471 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:55.475 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 08:25:55.475 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:55.475 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-09 08:25:55.476 | DEBUG | modules.decoder:decode      :501 - client_id:555 - 第0个chunk, 原始帧: 0~101,  torch.Size([1, 101, 80])
2025-07-09 08:25:55.476 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.553 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.554 | DEBUG | modules.decoder:decode_chunk:466 - client_id:555 - encoder 累计时点: 16
2025-07-09 08:25:55.554 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - ctc 搜索起始时点: 0 encoder_lens: tensor([16])
2025-07-09 08:25:55.554 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.555 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.572 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:55.572 | DEBUG | modules.decoder:search      :358 - ctc token&time [(4683, 12)]
2025-07-09 08:25:55.572 | DEBUG | modules.decoder:decode_chunk:481 - client_id:555 - ctc 累计搜索结果: "scientists"
2025-07-09 08:25:55.572 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:55.572 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists...
2025-07-09 08:25:55.572 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第0个数据包, 更新识别结果: "scientists"
2025-07-09 08:25:55.573 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:55.715 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:55.720 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 08:25:55.721 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:55.721 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-09 08:25:55.722 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:55.722 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:55.723 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:55.880 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:55.885 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 08:25:55.885 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:55.886 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-09 08:25:55.886 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:55.886 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:55.887 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:56.126 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:56.131 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 08:25:56.132 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:56.132 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-09 08:25:56.132 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:56.133 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:56.133 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:56.289 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:56.296 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 08:25:56.296 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:56.297 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-09 08:25:56.297 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:56.297 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:56.298 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:56.537 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:56.542 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 08:25:56.542 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:56.543 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-09 08:25:56.543 | DEBUG | modules.decoder:decode      :501 - client_id:111 - 第1个chunk, 原始帧: 96~197,  torch.Size([1, 101, 80])
2025-07-09 08:25:56.544 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.623 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.623 | DEBUG | modules.decoder:decode_chunk:466 - client_id:111 - encoder 累计时点: 32
2025-07-09 08:25:56.623 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - ctc 搜索起始时点: 16 encoder_lens: tensor([16])
2025-07-09 08:25:56.623 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.624 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.688 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:56.688 | DEBUG | modules.decoder:search      :358 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30)]
2025-07-09 08:25:56.688 | DEBUG | modules.decoder:decode_chunk:481 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made"
2025-07-09 08:25:56.688 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:56.688 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made...
2025-07-09 08:25:56.688 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "the surface of the moon is made"
2025-07-09 08:25:56.688 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:56.696 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:56.698 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 08:25:56.698 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:56.698 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-09 08:25:56.698 | DEBUG | modules.decoder:decode      :501 - client_id:555 - 第1个chunk, 原始帧: 96~197,  torch.Size([1, 101, 80])
2025-07-09 08:25:56.699 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.773 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.773 | DEBUG | modules.decoder:decode_chunk:466 - client_id:555 - encoder 累计时点: 32
2025-07-09 08:25:56.774 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - ctc 搜索起始时点: 16 encoder_lens: tensor([16])
2025-07-09 08:25:56.774 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.775 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.836 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:56.836 | DEBUG | modules.decoder:search      :358 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28)]
2025-07-09 08:25:56.836 | DEBUG | modules.decoder:decode_chunk:481 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how"
2025-07-09 08:25:56.836 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:56.836 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how...
2025-07-09 08:25:56.837 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第1个数据包, 更新识别结果: "scientists hope to understand how"
2025-07-09 08:25:56.837 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:56.947 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:56.952 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 08:25:56.952 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:56.952 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-09 08:25:56.953 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:56.953 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:56.953 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:57.107 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:57.112 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 08:25:57.112 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:57.113 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-09 08:25:57.113 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:57.113 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:57.113 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:57.356 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:57.361 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 08:25:57.361 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:57.362 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-09 08:25:57.362 | DEBUG | modules.decoder:decode      :501 - client_id:111 - 第2个chunk, 原始帧: 192~293,  torch.Size([1, 101, 80])
2025-07-09 08:25:57.363 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.442 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.442 | DEBUG | modules.decoder:decode_chunk:466 - client_id:111 - encoder 累计时点: 48
2025-07-09 08:25:57.443 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - ctc 搜索起始时点: 32 encoder_lens: tensor([16])
2025-07-09 08:25:57.443 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.444 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.512 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:57.512 | DEBUG | modules.decoder:search      :358 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44)]
2025-07-09 08:25:57.512 | DEBUG | modules.decoder:decode_chunk:481 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust"
2025-07-09 08:25:57.512 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:57.512 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust...
2025-07-09 08:25:57.512 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第2个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust"
2025-07-09 08:25:57.513 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:57.513 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:57.515 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 08:25:57.516 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:57.516 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-09 08:25:57.516 | DEBUG | modules.decoder:decode      :501 - client_id:555 - 第2个chunk, 原始帧: 192~293,  torch.Size([1, 101, 80])
2025-07-09 08:25:57.516 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.590 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.591 | DEBUG | modules.decoder:decode_chunk:466 - client_id:555 - encoder 累计时点: 48
2025-07-09 08:25:57.591 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - ctc 搜索起始时点: 32 encoder_lens: tensor([16])
2025-07-09 08:25:57.591 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.592 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.659 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:57.659 | DEBUG | modules.decoder:search      :358 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47)]
2025-07-09 08:25:57.659 | DEBUG | modules.decoder:decode_chunk:481 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how"
2025-07-09 08:25:57.659 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:57.659 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 08:25:57.659 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第2个数据包, 更新识别结果: "scientists hope to understand how plane form especially how"
2025-07-09 08:25:57.659 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:57.766 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:57.771 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 08:25:57.771 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:57.772 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-09 08:25:57.772 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:57.772 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:57.773 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:57.922 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:57.927 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 08:25:57.928 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:57.928 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-09 08:25:57.929 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:57.929 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:57.929 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:58.175 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:58.180 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 08:25:58.181 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:58.181 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-09 08:25:58.181 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:58.182 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:58.182 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:58.332 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:58.337 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 08:25:58.337 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:58.338 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-09 08:25:58.338 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:58.338 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:58.339 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:58.585 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:58.590 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 08:25:58.590 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:58.591 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-09 08:25:58.591 | DEBUG | modules.decoder:decode      :501 - client_id:111 - 第3个chunk, 原始帧: 288~389,  torch.Size([1, 101, 80])
2025-07-09 08:25:58.592 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.670 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.670 | DEBUG | modules.decoder:decode_chunk:466 - client_id:111 - encoder 累计时点: 64
2025-07-09 08:25:58.671 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - ctc 搜索起始时点: 48 encoder_lens: tensor([16])
2025-07-09 08:25:58.671 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.672 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.736 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:58.736 | DEBUG | modules.decoder:search      :358 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44), (5343, 49), (3805, 52), (1811, 54), (3047, 57), (3698, 61), (5343, 63)]
2025-07-09 08:25:58.736 | DEBUG | modules.decoder:decode_chunk:481 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust the outer layer of the"
2025-07-09 08:25:58.737 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:58.737 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust ...
2025-07-09 08:25:58.737 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第3个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the"
2025-07-09 08:25:58.737 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:58.738 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:58.740 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 08:25:58.740 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:58.740 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-09 08:25:58.740 | DEBUG | modules.decoder:decode      :501 - client_id:555 - 第3个chunk, 原始帧: 288~389,  torch.Size([1, 101, 80])
2025-07-09 08:25:58.740 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.813 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.813 | DEBUG | modules.decoder:decode_chunk:466 - client_id:555 - encoder 累计时点: 64
2025-07-09 08:25:58.814 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - ctc 搜索起始时点: 48 encoder_lens: tensor([16])
2025-07-09 08:25:58.814 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.815 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.878 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:58.878 | DEBUG | modules.decoder:search      :358 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62)]
2025-07-09 08:25:58.878 | DEBUG | modules.decoder:decode_chunk:481 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since"
2025-07-09 08:25:58.878 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:58.878 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 08:25:58.878 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第3个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since"
2025-07-09 08:25:58.879 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:58.995 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:58.999 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 08:25:59.000 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:59.000 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-09 08:25:59.001 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:59.001 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:59.001 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:59.148 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:59.153 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 08:25:59.153 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:59.154 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-09 08:25:59.155 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:59.155 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:59.155 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:59.404 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:59.409 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 08:25:59.409 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:59.410 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-09 08:25:59.410 | DEBUG | modules.decoder:decode      :501 - client_id:111 - 第4个chunk, 原始帧: 384~485,  torch.Size([1, 101, 80])
2025-07-09 08:25:59.411 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.495 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.496 | DEBUG | modules.decoder:decode_chunk:466 - client_id:111 - encoder 累计时点: 80
2025-07-09 08:25:59.496 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - ctc 搜索起始时点: 64 encoder_lens: tensor([16])
2025-07-09 08:25:59.497 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.498 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.563 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:59.563 | DEBUG | modules.decoder:search      :358 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44), (5343, 49), (3805, 52), (1811, 54), (3047, 57), (3698, 61), (5343, 63), (3467, 66), (2833, 70), (745, 74), (5343, 79)]
2025-07-09 08:25:59.563 | DEBUG | modules.decoder:decode_chunk:481 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the"
2025-07-09 08:25:59.563 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:59.563 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust ...
2025-07-09 08:25:59.563 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第4个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the"
2025-07-09 08:25:59.564 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:59.564 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:59.567 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 08:25:59.567 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:59.567 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-09 08:25:59.567 | DEBUG | modules.decoder:decode      :501 - client_id:555 - 第4个chunk, 原始帧: 384~485,  torch.Size([1, 101, 80])
2025-07-09 08:25:59.568 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.641 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.642 | DEBUG | modules.decoder:decode_chunk:466 - client_id:555 - encoder 累计时点: 80
2025-07-09 08:25:59.642 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - ctc 搜索起始时点: 64 encoder_lens: tensor([16])
2025-07-09 08:25:59.642 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.643 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.706 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:25:59.706 | DEBUG | modules.decoder:search      :358 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62), (1023, 65), (5246, 68), (4615, 69), (990, 71), (3152, 72), (2628, 73), (1659, 75), (5897, 76), (5343, 78)]
2025-07-09 08:25:59.706 | DEBUG | modules.decoder:decode_chunk:481 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the"
2025-07-09 08:25:59.706 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 08:25:59.706 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 08:25:59.706 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第4个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the"
2025-07-09 08:25:59.706 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:59.811 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:25:59.816 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 08:25:59.817 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:25:59.817 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-09 08:25:59.818 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:59.818 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 08:25:59.818 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:25:59.964 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:25:59.970 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 08:25:59.970 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:25:59.971 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-09 08:25:59.971 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:25:59.971 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:25:59.972 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:26:00.212 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 08:26:00.217 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 534
2025-07-09 08:26:00.217 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 08:26:00.218 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 534, 开始解码
2025-07-09 08:26:00.218 | DEBUG | modules.decoder:decode      :512 - client_id:111 - 第5个chunk, 原始帧: 480~534,  torch.Size([1, 54, 80])
2025-07-09 08:26:00.219 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.292 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.292 | DEBUG | modules.decoder:decode_chunk:466 - client_id:111 - encoder 累计时点: 88
2025-07-09 08:26:00.292 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - ctc 搜索起始时点: 80 encoder_lens: tensor([8])
2025-07-09 08:26:00.293 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.293 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.326 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:26:00.326 | DEBUG | modules.decoder:search      :358 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44), (5343, 49), (3805, 52), (1811, 54), (3047, 57), (3698, 61), (5343, 63), (3467, 66), (2833, 70), (745, 74), (5343, 79), (1269, 82), (5026, 85)]
2025-07-09 08:26:00.326 | INFO  | modules.decoder:detokenize  :532 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 08:26:00.326 | DEBUG | modules.decoder:decode_chunk:481 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the crust"
2025-07-09 08:26:00.326 | INFO  | modules.connect:on_decode   :922 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-09 08:26:00.326 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust ...
2025-07-09 08:26:00.326 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第5个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the crust"
2025-07-09 08:26:00.327 | INFO  | server :receive     :343 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 08:26:00.327 | INFO  | server :receive     :349 - client_id: 111 - 关闭连接，清理资源
2025-07-09 08:26:00.327 | DEBUG | modules.connect:disconnect  :284 - client_id:111 - 开始断开连接，当前活跃连接数: 2
2025-07-09 08:26:00.327 | DEBUG | modules.connect:disconnect  :287 - client_id:111 - 关闭WebSocket连接
2025-07-09 08:26:00.331 | DEBUG | modules.connect:disconnect  :290 - client_id:111 - WebSocket连接已关闭
2025-07-09 08:26:00.331 | DEBUG | modules.connect:disconnect  :298 - client_id:111 - 已从active_connection中移除
2025-07-09 08:26:00.331 | DEBUG | modules.connect:disconnect  :304 - client_id:111 - 开始清理客户端状态和缓存
2025-07-09 08:26:00.331 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:26:00.332 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:26:00.332 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 08:26:00.332 | DEBUG | modules.connect:disconnect  :317 - client_id:111 - 客户端状态已完全清理
2025-07-09 08:26:00.332 | DEBUG | modules.connect:disconnect  :324 - client_id:111 - 活跃连接数已更新: 1
2025-07-09 08:26:00.332 | DEBUG | server :websocket_endpoint:258 - client_id:111 - 协程完成，清理待处理任务
2025-07-09 08:26:00.371 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:26:00.376 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-09 08:26:00.376 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:26:00.377 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-09 08:26:00.377 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 08:26:00.378 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 08:26:00.378 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 08:26:00.774 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 08:26:00.790 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第15个数据包, 累计帧数: 586
2025-07-09 08:26:00.791 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 08:26:00.791 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 586, 开始解码
2025-07-09 08:26:00.791 | DEBUG | modules.decoder:decode      :501 - client_id:555 - 第5个chunk, 原始帧: 480~581,  torch.Size([1, 101, 80])
2025-07-09 08:26:00.792 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.867 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.867 | DEBUG | modules.decoder:decode_chunk:466 - client_id:555 - encoder 累计时点: 96
2025-07-09 08:26:00.867 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - ctc 搜索起始时点: 80 encoder_lens: tensor([16])
2025-07-09 08:26:00.868 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.868 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.932 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:26:00.932 | DEBUG | modules.decoder:search      :358 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62), (1023, 65), (5246, 68), (4615, 69), (990, 71), (3152, 72), (2628, 73), (1659, 75), (5897, 76), (5343, 78), (1643, 80), (3176, 85), (137, 90)]
2025-07-09 08:26:00.933 | INFO  | modules.decoder:detokenize  :532 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 08:26:00.933 | DEBUG | modules.decoder:decode_chunk:481 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-09 08:26:00.933 | DEBUG | modules.decoder:decode      :512 - client_id:555 - 第6个chunk, 原始帧: 576~586,  torch.Size([1, 10, 80])
2025-07-09 08:26:00.933 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.934 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.934 | DEBUG | modules.decoder:ort_run     :237 - encoder.innx 请求错误! [ONNXRuntimeError] : 2 : INVALID_ARGUMENT : Non-zero status code returned while running Conv node. Name:'/embed/conv/conv.3/Relu_output_0_nchwc' Status Message: Invalid input shape: {4,39}
2025-07-09 08:26:00.935 | DEBUG | modules.decoder:decode_chunk:466 - client_id:555 - encoder 累计时点: 96
2025-07-09 08:26:00.935 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - ctc 搜索起始时点: 96 encoder_lens: tensor([0])
2025-07-09 08:26:00.935 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.935 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.968 | DEBUG | modules.decoder:search      :357 - ctc 前缀集束搜索完成
2025-07-09 08:26:00.968 | DEBUG | modules.decoder:search      :358 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62), (1023, 65), (5246, 68), (4615, 69), (990, 71), (3152, 72), (2628, 73), (1659, 75), (5897, 76), (5343, 78), (1643, 80), (3176, 85), (137, 90)]
2025-07-09 08:26:00.968 | INFO  | modules.decoder:detokenize  :532 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 08:26:00.968 | DEBUG | modules.decoder:decode_chunk:481 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-09 08:26:00.968 | INFO  | modules.connect:on_decode   :922 - client_id:555 - *** 最后一个数据包完成解码 ***
2025-07-09 08:26:00.968 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 08:26:00.968 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第5个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-09 08:26:00.969 | INFO  | server :receive     :343 - client_id:555 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 08:26:00.969 | INFO  | server :receive     :349 - client_id: 555 - 关闭连接，清理资源
2025-07-09 08:26:00.969 | DEBUG | modules.connect:disconnect  :284 - client_id:555 - 开始断开连接，当前活跃连接数: 1
2025-07-09 08:26:00.969 | DEBUG | modules.connect:disconnect  :287 - client_id:555 - 关闭WebSocket连接
2025-07-09 08:26:00.970 | DEBUG | modules.connect:disconnect  :290 - client_id:555 - WebSocket连接已关闭
2025-07-09 08:26:00.970 | DEBUG | modules.connect:disconnect  :298 - client_id:555 - 已从active_connection中移除
2025-07-09 08:26:00.970 | DEBUG | modules.connect:disconnect  :304 - client_id:555 - 开始清理客户端状态和缓存
2025-07-09 08:26:00.970 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:26:00.970 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:26:00.971 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 08:26:00.971 | DEBUG | modules.connect:disconnect  :317 - client_id:555 - 客户端状态已完全清理
2025-07-09 08:26:00.971 | DEBUG | modules.connect:disconnect  :324 - client_id:555 - 活跃连接数已更新: 0
2025-07-09 08:26:00.971 | DEBUG | server :websocket_endpoint:258 - client_id:555 - 协程完成，清理待处理任务
