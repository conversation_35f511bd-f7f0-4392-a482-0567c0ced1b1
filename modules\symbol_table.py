import os
from typing import Dict, List

from modules.logger import logger
from utils.lat2ug import Lat2ug


class SymbolTable:
    def __init__(self, dict_path, lang_code, map_path=None, lower=False, remove_spm=False):
        self.dict = self.load_dict(dict_path)  # int: str
        self.char2id_dict = {v: k for k, v in self.dict.items()}  # str: int
        self.lang = lang_code
        if map_path and len(map_path) > 0:
            if os.path.exists(map_path):
                logger.info(f"加载映射表: {map_path}")
                self.L2U = Lat2ug(map_path)
            else:
                logger.warning(f"加载映射表失败, {map_path} 不存在")
        else:
            self.L2U = None
        
        self.lower = lower
        if lower:
            logger.debug(f"启用后处理: 转全小写字母")
        self.remove_spm = remove_spm
        if remove_spm:
            logger.debug(f"启用后处理: 去占位下划线")

    def load_dict(self, path: str) -> Dict:
        logger.debug(f"加载词表: {path}")
        char_dict = {}
        with open(path, 'r') as fin:
            for line in fin:
                arr = line.strip().split()
                assert len(arr) == 2
                char_dict[int(arr[1])] = arr[0]
        return char_dict


    def ids2tokens(self, ids: List[int]) -> List[str]:
        content = [self.dict[w] for w in ids]
        return content

    def char_map(self, text: str):
        # logger.info(f"raw_text: {text}")
        if self.lower:
            text = text.lower()
        if self.remove_spm:
            text = text.replace('▁', ' ').replace('  ', ' ').strip()
        if self.L2U:
            text = self.L2U.lat2ug(text)
        return text


