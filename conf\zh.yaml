# 模型加载
onnx_dir: /ws/MODELS/online_onnx_zh   # 模型路径
onnx_config: /ws/MODELS/online_onnx_zh/train.yaml  # 模型配置文件路径
fp16: false    # 是否使用单精度模型（适合双精度无法满足实时性的情况，达到加速）
quant: false   # 是否使用量化模型（适合双精度无法满足实时性的情况，达到加速）
device: cpu    # 可选：cpu/gpu/npu
device_id: 0   # 当 device: gpu 或 device: npu 时有效

# 热词功能（仅中文支持）
context_list_path: "/ws/MODELS/online_onnx_zh/hotwords.txt"   # 自定义热词文件路径
context_graph_score: 40   # 热词分数，增大此值将相应地增大解码结果中包含热词的概率

# 断句功能
separator_interval: 0.5   # 若说话间隙超过此值（秒），则添加分隔符
default_separator: "，"   # 中文默认分隔符（全角逗号） 若客户端请求消息中不含自定义分隔符，则启用此分隔符

# 其余后处理
lower: true
remove_spm: false
map_path:
