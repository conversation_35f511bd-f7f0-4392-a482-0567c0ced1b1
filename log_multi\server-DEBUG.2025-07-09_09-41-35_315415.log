2025-07-09 09:41:35.318 | INFO  | modules.config:init_logger :591 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-09 09:41:35.333 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 8081
2025-07-09 09:41:35.333 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-09 09:41:35.333 | INFO  | modules.config:init_monitoring:622 - 监控系统初始化成功
2025-07-09 09:41:35.334 | INFO  | modules.onnx_session_pool:__init__    :64 - ONNX会话池已启用, 每个模型最大会话数: 4
2025-07-09 09:41:35.336 | INFO  | modules.config:init_session_pool:647 - ONNX会话池初始化成功
2025-07-09 09:41:35.336 | INFO  | modules.config:init_all_modules:665 - 所有模块初始化完成
2025-07-09 09:41:35.337 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-09 09:41:35.337 | INFO  | server :lifespan    :88 - 启动多语种ASR服务模式: 预加载所有支持的语种模型, 自动识别语种并切换
2025-07-09 09:41:35.337 | INFO  | modules.asr_manager:load_models :56 - 多语种模式：预加载所有支持的语种模型
2025-07-09 09:41:35.337 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-09 09:41:35.337 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:36.780 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_encoder 注册成功，预创建会话: a924462e
2025-07-09 09:41:36.781 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-09 09:41:36.781 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:36.802 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_ctc 注册成功，预创建会话: 0a6ade5e
2025-07-09 09:41:36.802 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 zh 模型已注册到会话池
2025-07-09 09:41:36.808 | DEBUG | modules.config:build_model_config:204 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': '/ws/MODELS/online_onnx_zh/hotwords.txt', 'context_graph_score': 40, 'output_size': 512, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5538}
2025-07-09 09:41:36.809 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-09 09:41:36.815 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 09:41:36.816 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_en, fp16: False, quant: False, device: cpu
2025-07-09 09:41:36.816 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:38.722 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 en_encoder 注册成功，预创建会话: 05d65daa
2025-07-09 09:41:38.723 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_en, fp16: False, quant: False, device: cpu
2025-07-09 09:41:38.723 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:38.745 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 en_ctc 注册成功，预创建会话: 350bbaf3
2025-07-09 09:41:38.745 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 en 模型已注册到会话池
2025-07-09 09:41:38.749 | DEBUG | modules.config:build_model_config:204 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 101, 'subsampling_rate': 6, 'right_context': 10, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 512, 'num_blocks': 16, 'cnn_module_kernel': 31, 'head': 8, 'feature_size': 80, 'vocab_size': 5999}
2025-07-09 09:41:38.749 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_en/units.txt
2025-07-09 09:41:38.757 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 09:41:38.757 | DEBUG | modules.symbol_table:__init__    :27 - 启用后处理: 去占位下划线
2025-07-09 09:41:38.757 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ru, fp16: False, quant: True, device: cpu
2025-07-09 09:41:38.757 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:39.163 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 ru_encoder 注册成功，预创建会话: d78a2a98
2025-07-09 09:41:39.164 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ru, fp16: False, quant: True, device: cpu
2025-07-09 09:41:39.164 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:39.171 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 ru_ctc 注册成功，预创建会话: 00ad4dcc
2025-07-09 09:41:39.171 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 ru 模型已注册到会话池
2025-07-09 09:41:39.175 | DEBUG | modules.config:build_model_config:204 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5107}
2025-07-09 09:41:39.175 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_ru/units.txt
2025-07-09 09:41:39.182 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 09:41:39.182 | DEBUG | modules.symbol_table:__init__    :27 - 启用后处理: 去占位下划线
2025-07-09 09:41:39.182 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kk, fp16: False, quant: True, device: cpu
2025-07-09 09:41:39.182 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:39.687 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 kk_encoder 注册成功，预创建会话: cd488992
2025-07-09 09:41:39.688 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kk, fp16: False, quant: True, device: cpu
2025-07-09 09:41:39.688 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:39.695 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 kk_ctc 注册成功，预创建会话: 8920895b
2025-07-09 09:41:39.695 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 kk 模型已注册到会话池
2025-07-09 09:41:39.701 | DEBUG | modules.config:build_model_config:204 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 8, 'head': 4, 'feature_size': 80, 'vocab_size': 5075}
2025-07-09 09:41:39.701 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_kk/units.txt
2025-07-09 09:41:39.707 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-09 09:41:39.708 | DEBUG | modules.symbol_table:__init__    :27 - 启用后处理: 去占位下划线
2025-07-09 09:41:39.708 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kkin, fp16: False, quant: True, device: cpu
2025-07-09 09:41:39.708 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:40.132 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 kkin_encoder 注册成功，预创建会话: 7a013662
2025-07-09 09:41:40.132 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_kkin, fp16: False, quant: True, device: cpu
2025-07-09 09:41:40.132 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:40.140 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 kkin_ctc 注册成功，预创建会话: 8bb425ac
2025-07-09 09:41:40.141 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 kkin 模型已注册到会话池
2025-07-09 09:41:40.144 | DEBUG | modules.config:build_model_config:204 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 4, 'feature_size': 80, 'vocab_size': 5006}
2025-07-09 09:41:40.144 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_kkin/units.txt
2025-07-09 09:41:40.151 | INFO  | modules.symbol_table:__init__    :15 - 加载映射表: /ws/MODELS/online_onnx_kkin/map_kkin2lat.txt
2025-07-09 09:41:40.151 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ug, fp16: False, quant: True, device: cpu
2025-07-09 09:41:40.151 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:40.592 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 ug_encoder 注册成功，预创建会话: f920ad35
2025-07-09 09:41:40.593 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_ug, fp16: False, quant: True, device: cpu
2025-07-09 09:41:40.593 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:40.600 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 ug_ctc 注册成功，预创建会话: bb364064
2025-07-09 09:41:40.601 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 ug 模型已注册到会话池
2025-07-09 09:41:40.604 | DEBUG | modules.config:build_model_config:204 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': None, 'context_graph_score': 40.0, 'output_size': 256, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 4, 'feature_size': 80, 'vocab_size': 5002}
2025-07-09 09:41:40.604 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_ug/units.txt
2025-07-09 09:41:40.610 | INFO  | modules.symbol_table:__init__    :15 - 加载映射表: /ws/MODELS/online_onnx_ug/map_uyg2lat.txt
2025-07-09 09:41:40.611 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 6/6 个语种
2025-07-09 09:41:40.613 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-09 09:41:40.688 | DEBUG | modules.lid_manager:_load_lid_model:94 - LID模型加载成功: /ws/MODELS/lid_model/lid.onnx
2025-07-09 09:41:40.689 | DEBUG | modules.lid_manager:_load_lid_model:104 - LID语种字典加载成功: /ws/MODELS/lid_model/spk2id.json
2025-07-09 09:41:40.689 | DEBUG | modules.lid_manager:_load_lid_model:113 - 应用全局CMVN: /ws/MODELS/lid_model/global_cmvn
2025-07-09 09:41:40.690 | INFO  | server :lifespan    :106 - LID管理器初始化成功: /ws/MODELS/lid_model/lid.onnx
2025-07-09 09:41:40.690 | INFO  | server :lifespan    :118 - Server start, init manager, LID_MANAGER, ASR_MANAGER
2025-07-09 09:41:56.136 | INFO  | server :websocket_endpoint:231 - client_id:222 - 开始初始化连接
2025-07-09 09:41:56.136 | DEBUG | modules.connect:connect     :181 - client_id:222 - 开始接受WebSocket连接
2025-07-09 09:41:56.137 | DEBUG | modules.connect:connect     :183 - client_id:222 - WebSocket连接已接受
2025-07-09 09:41:56.137 | DEBUG | modules.connect:connect     :187 - client_id:222 - WebSocket连接已存储到active_connection
2025-07-09 09:41:56.137 | INFO  | server :websocket_endpoint:237 - client_id:222 - 连接初始化完成，当前活跃连接数: 1
2025-07-09 09:41:56.137 | DEBUG | server :websocket_endpoint:238 - client_id:222 - 连接状态: True
2025-07-09 09:41:56.138 | DEBUG | server :websocket_endpoint:239 - client_id:222 - WebSocket状态: WebSocketState.CONNECTED
2025-07-09 09:41:56.138 | DEBUG | server :websocket_endpoint:247 - client_id:222 - 启动消息接收协程
2025-07-09 09:41:56.138 | INFO  | server :websocket_endpoint:231 - client_id:111 - 开始初始化连接
2025-07-09 09:41:56.139 | DEBUG | modules.connect:connect     :181 - client_id:111 - 开始接受WebSocket连接
2025-07-09 09:41:56.139 | DEBUG | modules.connect:connect     :183 - client_id:111 - WebSocket连接已接受
2025-07-09 09:41:56.139 | DEBUG | modules.connect:connect     :187 - client_id:111 - WebSocket连接已存储到active_connection
2025-07-09 09:41:56.139 | INFO  | server :websocket_endpoint:237 - client_id:111 - 连接初始化完成，当前活跃连接数: 2
2025-07-09 09:41:56.139 | DEBUG | server :websocket_endpoint:238 - client_id:111 - 连接状态: True
2025-07-09 09:41:56.140 | DEBUG | server :websocket_endpoint:239 - client_id:111 - WebSocket状态: WebSocketState.CONNECTED
2025-07-09 09:41:56.140 | DEBUG | server :websocket_endpoint:247 - client_id:111 - 启动消息接收协程
2025-07-09 09:41:56.141 | INFO  | server :websocket_endpoint:231 - client_id:333 - 开始初始化连接
2025-07-09 09:41:56.141 | DEBUG | modules.connect:connect     :181 - client_id:333 - 开始接受WebSocket连接
2025-07-09 09:41:56.141 | DEBUG | modules.connect:connect     :183 - client_id:333 - WebSocket连接已接受
2025-07-09 09:41:56.141 | DEBUG | modules.connect:connect     :187 - client_id:333 - WebSocket连接已存储到active_connection
2025-07-09 09:41:56.142 | INFO  | server :websocket_endpoint:237 - client_id:333 - 连接初始化完成，当前活跃连接数: 3
2025-07-09 09:41:56.142 | DEBUG | server :websocket_endpoint:238 - client_id:333 - 连接状态: True
2025-07-09 09:41:56.142 | DEBUG | server :websocket_endpoint:239 - client_id:333 - WebSocket状态: WebSocketState.CONNECTED
2025-07-09 09:41:56.142 | DEBUG | server :websocket_endpoint:247 - client_id:333 - 启动消息接收协程
2025-07-09 09:41:56.143 | INFO  | server :websocket_endpoint:231 - client_id:444 - 开始初始化连接
2025-07-09 09:41:56.143 | DEBUG | modules.connect:connect     :181 - client_id:444 - 开始接受WebSocket连接
2025-07-09 09:41:56.143 | DEBUG | modules.connect:connect     :183 - client_id:444 - WebSocket连接已接受
2025-07-09 09:41:56.143 | DEBUG | modules.connect:connect     :187 - client_id:444 - WebSocket连接已存储到active_connection
2025-07-09 09:41:56.144 | INFO  | server :websocket_endpoint:237 - client_id:444 - 连接初始化完成，当前活跃连接数: 4
2025-07-09 09:41:56.144 | DEBUG | server :websocket_endpoint:238 - client_id:444 - 连接状态: True
2025-07-09 09:41:56.144 | DEBUG | server :websocket_endpoint:239 - client_id:444 - WebSocket状态: WebSocketState.CONNECTED
2025-07-09 09:41:56.144 | DEBUG | server :websocket_endpoint:247 - client_id:444 - 启动消息接收协程
2025-07-09 09:41:56.145 | INFO  | server :websocket_endpoint:231 - client_id:555 - 开始初始化连接
2025-07-09 09:41:56.145 | DEBUG | modules.connect:connect     :181 - client_id:555 - 开始接受WebSocket连接
2025-07-09 09:41:56.145 | DEBUG | modules.connect:connect     :183 - client_id:555 - WebSocket连接已接受
2025-07-09 09:41:56.145 | DEBUG | modules.connect:connect     :187 - client_id:555 - WebSocket连接已存储到active_connection
2025-07-09 09:41:56.146 | INFO  | server :websocket_endpoint:237 - client_id:555 - 连接初始化完成，当前活跃连接数: 5
2025-07-09 09:41:56.146 | DEBUG | server :websocket_endpoint:238 - client_id:555 - 连接状态: True
2025-07-09 09:41:56.146 | DEBUG | server :websocket_endpoint:239 - client_id:555 - WebSocket状态: WebSocketState.CONNECTED
2025-07-09 09:41:56.146 | DEBUG | server :websocket_endpoint:247 - client_id:555 - 启动消息接收协程
2025-07-09 09:41:56.147 | DEBUG | server :receive     :297 - client_id:222 - 进入消息接收循环
2025-07-09 09:41:56.147 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.148 | DEBUG | server :receive     :297 - client_id:111 - 进入消息接收循环
2025-07-09 09:41:56.148 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.148 | DEBUG | server :receive     :297 - client_id:333 - 进入消息接收循环
2025-07-09 09:41:56.148 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.149 | DEBUG | server :receive     :297 - client_id:444 - 进入消息接收循环
2025-07-09 09:41:56.149 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.149 | DEBUG | server :receive     :297 - client_id:555 - 进入消息接收循环
2025-07-09 09:41:56.150 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.305 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:41:56.306 | INFO  | modules.connect:on_check    :446 - client_id:222 - 设置自定义分隔符: ", "
2025-07-09 09:41:56.306 | INFO  | modules.connect:_handle_language_options:158 - client_id:222 - 客户端指定语种: en, 跳过LID
2025-07-09 09:41:56.307 | INFO  | modules.connect:_init_decoder:134 - client_id:222 - 初始化解码器, 使用默认语种: zh
2025-07-09 09:41:56.348 | INFO  | modules.connect:_init_decoder:141 - client_id:222 - 解码器初始化完成, 分隔符: ", "
2025-07-09 09:41:56.354 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 09:41:56.355 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:41:56.355 | DEBUG | modules.connect:on_decode   :934 - client_id:222 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-09 09:41:56.355 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 09:41:56.355 | INFO  | modules.connect:_switch_asr_model:849 - client_id:222 - 重新创建解码器以使用新语种配置
2025-07-09 09:41:56.355 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:41:56.356 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:41:56.356 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:41:56.358 | INFO  | modules.connect:_switch_asr_model:858 - client_id:222 - 成功切换到语种: en
2025-07-09 09:41:56.365 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:41:56.366 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.476 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:41:56.476 | INFO  | modules.connect:on_check    :446 - client_id:111 - 设置自定义分隔符: ", "
2025-07-09 09:41:56.476 | INFO  | modules.connect:_handle_language_options:158 - client_id:111 - 客户端指定语种: en, 跳过LID
2025-07-09 09:41:56.477 | INFO  | modules.connect:_init_decoder:134 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-09 09:41:56.478 | INFO  | modules.connect:_init_decoder:141 - client_id:111 - 解码器初始化完成, 分隔符: ", "
2025-07-09 09:41:56.483 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 09:41:56.484 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:41:56.484 | DEBUG | modules.connect:on_decode   :934 - client_id:111 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-09 09:41:56.485 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 09:41:56.485 | INFO  | modules.connect:_switch_asr_model:849 - client_id:111 - 重新创建解码器以使用新语种配置
2025-07-09 09:41:56.485 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:41:56.486 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:41:56.486 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:41:56.489 | INFO  | modules.connect:_switch_asr_model:858 - client_id:111 - 成功切换到语种: en
2025-07-09 09:41:56.495 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:41:56.495 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.622 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:41:56.623 | INFO  | modules.connect:on_check    :446 - client_id:333 - 设置自定义分隔符: ", "
2025-07-09 09:41:56.623 | INFO  | modules.connect:_handle_language_options:158 - client_id:333 - 客户端指定语种: en, 跳过LID
2025-07-09 09:41:56.623 | INFO  | modules.connect:_init_decoder:134 - client_id:333 - 初始化解码器, 使用默认语种: zh
2025-07-09 09:41:56.625 | INFO  | modules.connect:_init_decoder:141 - client_id:333 - 解码器初始化完成, 分隔符: ", "
2025-07-09 09:41:56.630 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 09:41:56.630 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:41:56.631 | DEBUG | modules.connect:on_decode   :934 - client_id:333 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-09 09:41:56.631 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 09:41:56.631 | INFO  | modules.connect:_switch_asr_model:849 - client_id:333 - 重新创建解码器以使用新语种配置
2025-07-09 09:41:56.632 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:41:56.632 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:41:56.632 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:41:56.636 | INFO  | modules.connect:_switch_asr_model:858 - client_id:333 - 成功切换到语种: en
2025-07-09 09:41:56.641 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:41:56.642 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.773 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:41:56.773 | INFO  | modules.connect:on_check    :446 - client_id:444 - 设置自定义分隔符: ", "
2025-07-09 09:41:56.774 | INFO  | modules.connect:_handle_language_options:158 - client_id:444 - 客户端指定语种: en, 跳过LID
2025-07-09 09:41:56.774 | INFO  | modules.connect:_init_decoder:134 - client_id:444 - 初始化解码器, 使用默认语种: zh
2025-07-09 09:41:56.775 | INFO  | modules.connect:_init_decoder:141 - client_id:444 - 解码器初始化完成, 分隔符: ", "
2025-07-09 09:41:56.780 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 09:41:56.780 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:41:56.781 | DEBUG | modules.connect:on_decode   :934 - client_id:444 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-09 09:41:56.781 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 09:41:56.781 | INFO  | modules.connect:_switch_asr_model:849 - client_id:444 - 重新创建解码器以使用新语种配置
2025-07-09 09:41:56.782 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:41:56.782 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:41:56.782 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:41:56.786 | INFO  | modules.connect:_switch_asr_model:858 - client_id:444 - 成功切换到语种: en
2025-07-09 09:41:56.791 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:41:56.792 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.925 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:41:56.925 | INFO  | modules.connect:on_check    :446 - client_id:555 - 设置自定义分隔符: ", "
2025-07-09 09:41:56.926 | INFO  | modules.connect:_handle_language_options:158 - client_id:555 - 客户端指定语种: en, 跳过LID
2025-07-09 09:41:56.926 | INFO  | modules.connect:_init_decoder:134 - client_id:555 - 初始化解码器, 使用默认语种: zh
2025-07-09 09:41:56.927 | INFO  | modules.connect:_init_decoder:141 - client_id:555 - 解码器初始化完成, 分隔符: ", "
2025-07-09 09:41:56.932 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 09:41:56.932 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:41:56.933 | DEBUG | modules.connect:on_decode   :934 - client_id:555 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-09 09:41:56.933 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 09:41:56.933 | INFO  | modules.connect:_switch_asr_model:849 - client_id:555 - 重新创建解码器以使用新语种配置
2025-07-09 09:41:56.934 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:41:56.934 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:41:56.934 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:41:56.938 | INFO  | modules.connect:_switch_asr_model:858 - client_id:555 - 成功切换到语种: en
2025-07-09 09:41:56.940 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:41:56.943 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 09:41:56.943 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:41:56.944 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-09 09:41:56.944 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:41:56.944 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:41:56.944 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.945 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:41:56.947 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 09:41:56.947 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:41:56.947 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-09 09:41:56.947 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:41:56.947 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:41:56.947 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:56.948 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:41:56.948 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:57.028 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:41:57.033 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 09:41:57.033 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:41:57.033 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-09 09:41:57.034 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:41:57.034 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:41:57.034 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:57.178 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:41:57.183 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 09:41:57.183 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:41:57.184 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-09 09:41:57.184 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:41:57.185 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:41:57.185 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:57.333 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:41:57.338 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 09:41:57.338 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:41:57.339 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-09 09:41:57.339 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:41:57.340 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:41:57.340 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:41:57.341 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:41:57.346 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 09:41:57.346 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:41:57.347 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-09 09:41:57.347 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第0个chunk, 原始帧: 0~101,  torch.Size([1, 101, 80])
2025-07-09 09:41:57.348 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:41:57.348 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:00.452 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:00.452 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:00.452 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:00.452 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 16
2025-07-09 09:42:00.453 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 0 encoder_lens: tensor([16])
2025-07-09 09:42:00.453 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:00.453 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:00.464 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:00.464 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:00.464 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:00.481 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:00.481 | DEBUG | modules.decoder:search      :359 - ctc token&time []
2025-07-09 09:42:00.481 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: ""
2025-07-09 09:42:00.481 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:00.481 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:42:00.481 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:00.488 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:00.491 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 09:42:00.491 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:00.491 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-09 09:42:00.491 | DEBUG | modules.decoder:decode      :502 - client_id:111 - 第0个chunk, 原始帧: 0~101,  torch.Size([1, 101, 80])
2025-07-09 09:42:00.492 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:00.492 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:03.575 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:03.576 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:03.576 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:03.576 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - encoder 累计时点: 16
2025-07-09 09:42:03.576 | DEBUG | modules.decoder:decode_chunk:468 - client_id:111 - ctc 搜索起始时点: 0 encoder_lens: tensor([16])
2025-07-09 09:42:03.577 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:03.577 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:03.577 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:03.578 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:03.578 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:03.594 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:03.594 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 12)]
2025-07-09 09:42:03.594 | DEBUG | modules.decoder:decode_chunk:482 - client_id:111 - ctc 累计搜索结果: "the"
2025-07-09 09:42:03.595 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:03.595 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the...
2025-07-09 09:42:03.595 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "the"
2025-07-09 09:42:03.595 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:03.595 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:03.598 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 09:42:03.598 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:03.598 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-09 09:42:03.598 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第0个chunk, 原始帧: 0~101,  torch.Size([1, 101, 80])
2025-07-09 09:42:03.598 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:03.599 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:06.683 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:06.684 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:06.684 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:06.684 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 16
2025-07-09 09:42:06.684 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 0 encoder_lens: tensor([16])
2025-07-09 09:42:06.685 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:06.685 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:06.685 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:06.686 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:06.686 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:06.702 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:06.702 | DEBUG | modules.decoder:search      :359 - ctc token&time []
2025-07-09 09:42:06.702 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: ""
2025-07-09 09:42:06.702 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:06.703 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:42:06.703 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:06.703 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:06.705 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 09:42:06.705 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:06.705 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-09 09:42:06.706 | DEBUG | modules.decoder:decode      :502 - client_id:444 - 第0个chunk, 原始帧: 0~101,  torch.Size([1, 101, 80])
2025-07-09 09:42:06.706 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:06.706 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:09.789 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:09.789 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:09.789 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:09.789 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 16
2025-07-09 09:42:09.790 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 0 encoder_lens: tensor([16])
2025-07-09 09:42:09.790 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:09.790 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:09.791 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:09.791 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:09.791 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:09.807 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:09.807 | DEBUG | modules.decoder:search      :359 - ctc token&time []
2025-07-09 09:42:09.808 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: ""
2025-07-09 09:42:09.808 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:09.808 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:42:09.808 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:09.808 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:09.810 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 09:42:09.810 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:09.810 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-09 09:42:09.811 | DEBUG | modules.decoder:decode      :502 - client_id:555 - 第0个chunk, 原始帧: 0~101,  torch.Size([1, 101, 80])
2025-07-09 09:42:09.811 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:09.811 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:12.896 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:12.896 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:12.896 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:12.897 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - encoder 累计时点: 16
2025-07-09 09:42:12.897 | DEBUG | modules.decoder:decode_chunk:468 - client_id:555 - ctc 搜索起始时点: 0 encoder_lens: tensor([16])
2025-07-09 09:42:12.897 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:12.897 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:12.898 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:12.898 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:12.898 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:12.915 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:12.915 | DEBUG | modules.decoder:search      :359 - ctc token&time [(4683, 12)]
2025-07-09 09:42:12.915 | DEBUG | modules.decoder:decode_chunk:482 - client_id:555 - ctc 累计搜索结果: "scientists"
2025-07-09 09:42:12.915 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:12.915 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists...
2025-07-09 09:42:12.915 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第0个数据包, 更新识别结果: "scientists"
2025-07-09 09:42:12.916 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.916 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:12.918 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 09:42:12.918 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:12.918 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-09 09:42:12.919 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.919 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:42:12.919 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.927 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:12.929 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 09:42:12.929 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:12.929 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-09 09:42:12.929 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.929 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:42:12.929 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.930 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:12.931 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 09:42:12.932 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:12.932 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-09 09:42:12.932 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.932 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:42:12.932 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.932 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:12.934 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 09:42:12.934 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:12.934 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-09 09:42:12.934 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.934 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:42:12.935 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.935 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:12.936 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 09:42:12.937 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:12.937 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-09 09:42:12.937 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.937 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:42:12.937 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.937 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:12.939 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 09:42:12.939 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:12.939 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-09 09:42:12.939 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.939 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:42:12.939 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.940 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:12.942 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 09:42:12.942 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:12.942 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-09 09:42:12.942 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.943 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:42:12.943 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.943 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:12.944 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 09:42:12.945 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:12.945 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-09 09:42:12.945 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.945 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:42:12.945 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.945 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:12.947 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 09:42:12.947 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:12.947 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-09 09:42:12.947 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.947 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:42:12.948 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.948 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:12.949 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 09:42:12.950 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:12.950 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-09 09:42:12.950 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:12.950 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:42:12.950 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:12.950 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:12.952 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 09:42:12.952 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:12.952 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-09 09:42:12.952 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第1个chunk, 原始帧: 96~197,  torch.Size([1, 101, 80])
2025-07-09 09:42:12.952 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:12.953 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:16.037 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:16.037 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:16.037 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:16.037 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 32
2025-07-09 09:42:16.038 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 0 encoder_lens: tensor([32])
2025-07-09 09:42:16.038 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:16.038 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:16.039 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:16.039 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:16.039 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:16.093 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:16.093 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28)]
2025-07-09 09:42:16.093 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two"
2025-07-09 09:42:16.093 | DEBUG | modules.connect:on_decode   :927 - client_id:222 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:16.093 | DEBUG | server :receive     :332 - client_id:222 - 解码成功，发送结果: swirl the two...
2025-07-09 09:42:16.093 | INFO  | modules.connect:on_result   :409 - client_id:222 - <<< [发送] 第0个数据包, 更新识别结果: "swirl the two"
2025-07-09 09:42:16.093 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:16.094 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:16.096 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 09:42:16.096 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:16.096 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-09 09:42:16.097 | DEBUG | modules.decoder:decode      :502 - client_id:111 - 第1个chunk, 原始帧: 96~197,  torch.Size([1, 101, 80])
2025-07-09 09:42:16.097 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:16.097 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:19.181 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:19.182 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:19.182 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:19.182 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - encoder 累计时点: 32
2025-07-09 09:42:19.182 | DEBUG | modules.decoder:decode_chunk:468 - client_id:111 - ctc 搜索起始时点: 16 encoder_lens: tensor([16])
2025-07-09 09:42:19.182 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:19.183 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:19.183 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:19.183 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:19.183 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:19.219 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:19.219 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30)]
2025-07-09 09:42:19.219 | DEBUG | modules.decoder:decode_chunk:482 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made"
2025-07-09 09:42:19.219 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:19.219 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made...
2025-07-09 09:42:19.219 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "the surface of the moon is made"
2025-07-09 09:42:19.220 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:19.220 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:19.222 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 09:42:19.222 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:19.222 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-09 09:42:19.222 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第1个chunk, 原始帧: 96~197,  torch.Size([1, 101, 80])
2025-07-09 09:42:19.223 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:19.223 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:22.306 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:22.306 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:22.307 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:22.307 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 32
2025-07-09 09:42:22.307 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 0 encoder_lens: tensor([32])
2025-07-09 09:42:22.307 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:22.307 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:22.308 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:22.308 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:22.309 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:22.360 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:22.360 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28)]
2025-07-09 09:42:22.360 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the"
2025-07-09 09:42:22.360 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:22.361 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the...
2025-07-09 09:42:22.361 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第0个数据包, 更新识别结果: "the"
2025-07-09 09:42:22.361 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:22.361 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:22.363 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 09:42:22.364 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:22.364 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-09 09:42:22.364 | DEBUG | modules.decoder:decode      :502 - client_id:444 - 第1个chunk, 原始帧: 96~197,  torch.Size([1, 101, 80])
2025-07-09 09:42:22.364 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:22.364 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:25.452 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:25.452 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:25.452 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:25.452 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 32
2025-07-09 09:42:25.453 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 0 encoder_lens: tensor([32])
2025-07-09 09:42:25.453 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:25.453 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:25.454 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:25.454 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:25.454 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:25.510 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:25.510 | DEBUG | modules.decoder:search      :359 - ctc token&time [(215, 19), (923, 22), (1283, 25), (4615, 30)]
2025-07-09 09:42:25.510 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: "ancient cultures"
2025-07-09 09:42:25.510 | DEBUG | modules.connect:on_decode   :927 - client_id:444 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:25.510 | DEBUG | server :receive     :332 - client_id:444 - 解码成功，发送结果: ancient cultures...
2025-07-09 09:42:25.510 | INFO  | modules.connect:on_result   :409 - client_id:444 - <<< [发送] 第0个数据包, 更新识别结果: "ancient cultures"
2025-07-09 09:42:25.511 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:25.511 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:25.513 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 09:42:25.513 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:25.514 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-09 09:42:25.514 | DEBUG | modules.decoder:decode      :502 - client_id:555 - 第1个chunk, 原始帧: 96~197,  torch.Size([1, 101, 80])
2025-07-09 09:42:25.514 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:25.514 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:28.604 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:28.604 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:28.604 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:28.605 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - encoder 累计时点: 32
2025-07-09 09:42:28.605 | DEBUG | modules.decoder:decode_chunk:468 - client_id:555 - ctc 搜索起始时点: 16 encoder_lens: tensor([16])
2025-07-09 09:42:28.605 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:28.605 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:28.606 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:28.606 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:28.606 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:28.642 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:28.642 | DEBUG | modules.decoder:search      :359 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28)]
2025-07-09 09:42:28.642 | DEBUG | modules.decoder:decode_chunk:482 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how"
2025-07-09 09:42:28.642 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:28.642 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how...
2025-07-09 09:42:28.642 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第1个数据包, 更新识别结果: "scientists hope to understand how"
2025-07-09 09:42:28.643 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:28.643 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:28.645 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 09:42:28.645 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:28.645 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-09 09:42:28.646 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:28.646 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:42:28.646 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:28.647 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:28.649 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 09:42:28.650 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:28.650 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-09 09:42:28.650 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:28.650 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:42:28.650 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:28.650 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:28.652 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 09:42:28.652 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:28.652 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-09 09:42:28.652 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:28.653 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:42:28.653 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:28.653 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:28.655 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 09:42:28.655 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:28.655 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-09 09:42:28.655 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:28.655 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:42:28.655 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:28.655 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:28.657 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 09:42:28.657 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:28.658 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-09 09:42:28.658 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:28.658 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:42:28.658 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:28.658 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:28.660 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 09:42:28.660 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:28.660 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-09 09:42:28.660 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第2个chunk, 原始帧: 192~293,  torch.Size([1, 101, 80])
2025-07-09 09:42:28.661 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:28.661 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:31.744 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:31.744 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:31.744 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:31.745 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 48
2025-07-09 09:42:31.745 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 32 encoder_lens: tensor([16])
2025-07-09 09:42:31.745 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:31.745 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:31.746 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:31.746 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:31.746 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:31.798 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:31.798 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28), (1609, 33), (4092, 38), (4615, 41), (5424, 46)]
2025-07-09 09:42:31.799 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two dry powers together"
2025-07-09 09:42:31.799 | DEBUG | modules.connect:on_decode   :927 - client_id:222 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:31.799 | DEBUG | server :receive     :332 - client_id:222 - 解码成功，发送结果: swirl the two dry powers together...
2025-07-09 09:42:31.799 | INFO  | modules.connect:on_result   :409 - client_id:222 - <<< [发送] 第1个数据包, 更新识别结果: "swirl the two dry powers together"
2025-07-09 09:42:31.799 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:31.800 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:31.802 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 09:42:31.802 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:31.802 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-09 09:42:31.803 | DEBUG | modules.decoder:decode      :502 - client_id:111 - 第2个chunk, 原始帧: 192~293,  torch.Size([1, 101, 80])
2025-07-09 09:42:31.803 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:31.803 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:34.894 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:34.894 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:34.894 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:34.895 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - encoder 累计时点: 48
2025-07-09 09:42:34.895 | DEBUG | modules.decoder:decode_chunk:468 - client_id:111 - ctc 搜索起始时点: 32 encoder_lens: tensor([16])
2025-07-09 09:42:34.895 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:34.895 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:34.897 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:34.897 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:34.897 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:34.933 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:34.933 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44)]
2025-07-09 09:42:34.933 | DEBUG | modules.decoder:decode_chunk:482 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust"
2025-07-09 09:42:34.933 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:34.933 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust...
2025-07-09 09:42:34.933 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第2个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust"
2025-07-09 09:42:34.934 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:34.934 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:34.936 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 09:42:34.936 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:34.937 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-09 09:42:34.937 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第2个chunk, 原始帧: 192~293,  torch.Size([1, 101, 80])
2025-07-09 09:42:34.937 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:34.937 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:38.022 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:38.022 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:38.022 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:38.022 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 48
2025-07-09 09:42:38.023 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 32 encoder_lens: tensor([16])
2025-07-09 09:42:38.023 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:38.023 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:38.024 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:38.024 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:38.024 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:38.076 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:38.076 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46)]
2025-07-09 09:42:38.076 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the"
2025-07-09 09:42:38.077 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:38.077 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the...
2025-07-09 09:42:38.077 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第1个数据包, 更新识别结果: "the city is also the"
2025-07-09 09:42:38.077 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:38.077 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:38.079 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 09:42:38.080 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:38.080 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-09 09:42:38.080 | DEBUG | modules.decoder:decode      :502 - client_id:444 - 第2个chunk, 原始帧: 192~293,  torch.Size([1, 101, 80])
2025-07-09 09:42:38.080 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:38.080 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:41.165 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:41.165 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:41.165 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:41.166 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 48
2025-07-09 09:42:41.166 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 32 encoder_lens: tensor([16])
2025-07-09 09:42:41.166 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:41.166 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:41.167 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:41.167 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:41.167 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:41.219 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:41.220 | DEBUG | modules.decoder:search      :359 - ctc token&time [(215, 19), (923, 22), (1283, 25), (4615, 30), (223, 32), (5499, 35), (505, 44), (5422, 47)]
2025-07-09 09:42:41.220 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: "ancient cultures and tribes began to"
2025-07-09 09:42:41.220 | DEBUG | modules.connect:on_decode   :927 - client_id:444 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:41.220 | DEBUG | server :receive     :332 - client_id:444 - 解码成功，发送结果: ancient cultures and tribes began to...
2025-07-09 09:42:41.220 | INFO  | modules.connect:on_result   :409 - client_id:444 - <<< [发送] 第1个数据包, 更新识别结果: "ancient cultures and tribes began to"
2025-07-09 09:42:41.220 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:41.220 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:41.231 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 09:42:41.231 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:41.231 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-09 09:42:41.231 | DEBUG | modules.decoder:decode      :502 - client_id:555 - 第2个chunk, 原始帧: 192~293,  torch.Size([1, 101, 80])
2025-07-09 09:42:41.232 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:41.232 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:44.318 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:44.318 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:44.318 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:44.318 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - encoder 累计时点: 48
2025-07-09 09:42:44.319 | DEBUG | modules.decoder:decode_chunk:468 - client_id:555 - ctc 搜索起始时点: 32 encoder_lens: tensor([16])
2025-07-09 09:42:44.319 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:44.319 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:44.320 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:44.320 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:44.320 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:44.355 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:44.356 | DEBUG | modules.decoder:search      :359 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47)]
2025-07-09 09:42:44.356 | DEBUG | modules.decoder:decode_chunk:482 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how"
2025-07-09 09:42:44.356 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:44.356 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 09:42:44.356 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第2个数据包, 更新识别结果: "scientists hope to understand how plane form especially how"
2025-07-09 09:42:44.356 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.357 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:44.359 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 09:42:44.359 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:44.359 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-09 09:42:44.359 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.360 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:42:44.360 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.360 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:44.362 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 09:42:44.363 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:44.363 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-09 09:42:44.363 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.363 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:42:44.363 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.363 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:44.365 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 09:42:44.365 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:44.365 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-09 09:42:44.366 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.366 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:42:44.366 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.366 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:44.368 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 09:42:44.368 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:44.368 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-09 09:42:44.368 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.369 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:42:44.369 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.369 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:44.371 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 09:42:44.371 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:44.371 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-09 09:42:44.371 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.371 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:42:44.371 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.371 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:44.373 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 09:42:44.373 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:44.373 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-09 09:42:44.374 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.374 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:42:44.374 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.374 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:44.376 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 09:42:44.376 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:44.376 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-09 09:42:44.376 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.377 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:42:44.377 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.377 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:44.378 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 09:42:44.379 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:44.379 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-09 09:42:44.379 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.379 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:42:44.379 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.379 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:44.381 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 09:42:44.381 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:44.381 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-09 09:42:44.382 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.382 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:42:44.382 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.382 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:44.384 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 09:42:44.384 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:44.384 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-09 09:42:44.384 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:42:44.384 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:42:44.384 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:44.384 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:42:44.386 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 09:42:44.386 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:42:44.386 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-09 09:42:44.387 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第3个chunk, 原始帧: 288~389,  torch.Size([1, 101, 80])
2025-07-09 09:42:44.387 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:44.387 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:47.471 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:47.471 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:47.471 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:47.471 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 64
2025-07-09 09:42:47.472 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 48 encoder_lens: tensor([16])
2025-07-09 09:42:47.472 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:47.472 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:47.473 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:47.473 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:47.473 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:47.509 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:47.509 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28), (1609, 33), (4092, 38), (4615, 41), (5424, 46), (223, 50), (5348, 53), (5897, 57)]
2025-07-09 09:42:47.509 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two dry powers together and then with"
2025-07-09 09:42:47.509 | DEBUG | modules.connect:on_decode   :927 - client_id:222 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:47.509 | DEBUG | server :receive     :332 - client_id:222 - 解码成功，发送结果: swirl the two dry powers together and then with...
2025-07-09 09:42:47.509 | INFO  | modules.connect:on_result   :409 - client_id:222 - <<< [发送] 第2个数据包, 更新识别结果: "swirl the two dry powers together and then with"
2025-07-09 09:42:47.509 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:47.510 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:42:47.512 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 09:42:47.512 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:42:47.513 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-09 09:42:47.513 | DEBUG | modules.decoder:decode      :502 - client_id:111 - 第3个chunk, 原始帧: 288~389,  torch.Size([1, 101, 80])
2025-07-09 09:42:47.513 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:47.513 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:50.597 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:50.597 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:50.597 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:50.597 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - encoder 累计时点: 64
2025-07-09 09:42:50.598 | DEBUG | modules.decoder:decode_chunk:468 - client_id:111 - ctc 搜索起始时点: 48 encoder_lens: tensor([16])
2025-07-09 09:42:50.598 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:50.598 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:50.599 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:50.599 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:50.599 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:50.635 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:50.635 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44), (5343, 49), (3805, 52), (1811, 54), (3047, 57), (3698, 61), (5343, 63)]
2025-07-09 09:42:50.635 | DEBUG | modules.decoder:decode_chunk:482 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust the outer layer of the"
2025-07-09 09:42:50.636 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:50.636 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust ...
2025-07-09 09:42:50.636 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第3个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the"
2025-07-09 09:42:50.636 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:50.636 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:42:50.638 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 09:42:50.639 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:42:50.639 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-09 09:42:50.639 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第3个chunk, 原始帧: 288~389,  torch.Size([1, 101, 80])
2025-07-09 09:42:50.639 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:50.639 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:53.758 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:53.759 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:53.759 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:53.759 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 64
2025-07-09 09:42:53.760 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 48 encoder_lens: tensor([16])
2025-07-09 09:42:53.760 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:53.760 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:53.760 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:53.761 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:53.761 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:53.798 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:53.798 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58)]
2025-07-09 09:42:53.798 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb"
2025-07-09 09:42:53.798 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:53.798 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb...
2025-07-09 09:42:53.798 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第2个数据包, 更新识别结果: "the city is also the base to climb"
2025-07-09 09:42:53.798 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:53.799 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:42:53.801 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 09:42:53.801 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:42:53.801 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-09 09:42:53.802 | DEBUG | modules.decoder:decode      :502 - client_id:444 - 第3个chunk, 原始帧: 288~389,  torch.Size([1, 101, 80])
2025-07-09 09:42:53.802 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:53.802 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:42:56.912 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:42:56.912 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:42:56.912 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:42:56.913 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 64
2025-07-09 09:42:56.913 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 48 encoder_lens: tensor([16])
2025-07-09 09:42:56.913 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:42:56.913 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:42:56.914 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:42:56.914 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:42:56.914 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:42:56.961 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:42:56.961 | DEBUG | modules.decoder:search      :359 - ctc token&time [(215, 19), (923, 22), (1283, 25), (4615, 30), (223, 32), (5499, 35), (505, 44), (5422, 47), (2946, 50), (5346, 53), (2128, 56), (1650, 60)]
2025-07-09 09:42:56.962 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: "ancient cultures and tribes began to keep them for easy"
2025-07-09 09:42:56.962 | DEBUG | modules.connect:on_decode   :927 - client_id:444 - chunk 完成解码, 更新识别结果
2025-07-09 09:42:56.962 | DEBUG | server :receive     :332 - client_id:444 - 解码成功，发送结果: ancient cultures and tribes began to keep them for...
2025-07-09 09:42:56.962 | INFO  | modules.connect:on_result   :409 - client_id:444 - <<< [发送] 第2个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy"
2025-07-09 09:42:56.962 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:42:56.962 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:42:56.966 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 09:42:56.967 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:42:56.967 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-09 09:42:56.967 | DEBUG | modules.decoder:decode      :502 - client_id:555 - 第3个chunk, 原始帧: 288~389,  torch.Size([1, 101, 80])
2025-07-09 09:42:56.967 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:42:56.967 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:00.064 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:00.064 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:00.064 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:00.064 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - encoder 累计时点: 64
2025-07-09 09:43:00.065 | DEBUG | modules.decoder:decode_chunk:468 - client_id:555 - ctc 搜索起始时点: 48 encoder_lens: tensor([16])
2025-07-09 09:43:00.065 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:00.065 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:00.066 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:00.066 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:00.066 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:00.110 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:00.111 | DEBUG | modules.decoder:search      :359 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62)]
2025-07-09 09:43:00.111 | DEBUG | modules.decoder:decode_chunk:482 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since"
2025-07-09 09:43:00.111 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:00.111 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 09:43:00.111 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第3个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since"
2025-07-09 09:43:00.111 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:00.112 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:00.114 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 09:43:00.114 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:00.114 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-09 09:43:00.114 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:00.115 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:43:00.115 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:00.115 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:43:00.118 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 09:43:00.119 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:43:00.119 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-09 09:43:00.119 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:00.119 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:43:00.119 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:00.119 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:00.121 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 09:43:00.121 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:00.121 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-09 09:43:00.122 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:00.122 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:00.122 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:00.122 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:00.124 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 09:43:00.124 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:00.124 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-09 09:43:00.124 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:00.124 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:43:00.124 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:00.124 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:43:00.126 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 09:43:00.127 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:43:00.127 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-09 09:43:00.127 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:00.127 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:43:00.127 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:00.127 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:00.129 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 09:43:00.129 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:00.130 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-09 09:43:00.130 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第4个chunk, 原始帧: 384~485,  torch.Size([1, 101, 80])
2025-07-09 09:43:00.130 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:00.130 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:03.226 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:03.226 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:03.226 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:03.227 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 80
2025-07-09 09:43:03.227 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 64 encoder_lens: tensor([16])
2025-07-09 09:43:03.227 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:03.227 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:03.228 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:03.228 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:03.228 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:03.269 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:03.269 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28), (1609, 33), (4092, 38), (4615, 41), (5424, 46), (223, 50), (5348, 53), (5897, 57), (960, 64), (5804, 69), (5246, 72), (2434, 77)]
2025-07-09 09:43:03.269 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two dry powers together and then with clean wet hands"
2025-07-09 09:43:03.270 | DEBUG | modules.connect:on_decode   :927 - client_id:222 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:03.270 | DEBUG | server :receive     :332 - client_id:222 - 解码成功，发送结果: swirl the two dry powers together and then with cl...
2025-07-09 09:43:03.270 | INFO  | modules.connect:on_result   :409 - client_id:222 - <<< [发送] 第3个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands"
2025-07-09 09:43:03.270 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:03.271 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:43:03.273 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 09:43:03.274 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:43:03.274 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-09 09:43:03.274 | DEBUG | modules.decoder:decode      :502 - client_id:111 - 第4个chunk, 原始帧: 384~485,  torch.Size([1, 101, 80])
2025-07-09 09:43:03.274 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:03.274 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:06.371 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:06.371 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:06.371 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:06.372 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - encoder 累计时点: 80
2025-07-09 09:43:06.372 | DEBUG | modules.decoder:decode_chunk:468 - client_id:111 - ctc 搜索起始时点: 64 encoder_lens: tensor([16])
2025-07-09 09:43:06.372 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:06.372 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:06.373 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:06.373 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:06.373 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:06.409 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:06.410 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44), (5343, 49), (3805, 52), (1811, 54), (3047, 57), (3698, 61), (5343, 63), (3467, 66), (2833, 70), (745, 74), (5343, 79)]
2025-07-09 09:43:06.410 | DEBUG | modules.decoder:decode_chunk:482 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the"
2025-07-09 09:43:06.410 | DEBUG | modules.connect:on_decode   :927 - client_id:111 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:06.410 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust ...
2025-07-09 09:43:06.410 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第4个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the"
2025-07-09 09:43:06.411 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:06.411 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:06.413 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 09:43:06.413 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:06.414 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-09 09:43:06.414 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第4个chunk, 原始帧: 384~485,  torch.Size([1, 101, 80])
2025-07-09 09:43:06.414 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:06.414 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:09.498 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:09.498 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:09.498 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:09.498 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 80
2025-07-09 09:43:09.499 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 64 encoder_lens: tensor([16])
2025-07-09 09:43:09.499 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:09.499 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:09.500 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:09.500 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:09.500 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:09.536 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:09.536 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75)]
2025-07-09 09:43:09.536 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the nira"
2025-07-09 09:43:09.536 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:09.536 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb the nira...
2025-07-09 09:43:09.536 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第3个数据包, 更新识别结果: "the city is also the base to climb the nira"
2025-07-09 09:43:09.537 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:09.537 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:09.539 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 09:43:09.539 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:09.539 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-09 09:43:09.540 | DEBUG | modules.decoder:decode      :502 - client_id:444 - 第4个chunk, 原始帧: 384~485,  torch.Size([1, 101, 80])
2025-07-09 09:43:09.540 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:09.540 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:12.623 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:12.623 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:12.623 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:12.624 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 80
2025-07-09 09:43:12.624 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 64 encoder_lens: tensor([16])
2025-07-09 09:43:12.624 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:12.624 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:12.625 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:12.625 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:12.625 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:12.661 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:12.661 | DEBUG | modules.decoder:search      :359 - ctc token&time [(215, 19), (923, 22), (1283, 25), (4615, 30), (223, 32), (5499, 35), (505, 44), (5422, 47), (2946, 50), (5346, 53), (2128, 56), (1650, 60), (37, 66), (5422, 73), (3393, 79)]
2025-07-09 09:43:12.661 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: "ancient cultures and tribes began to keep them for easy access to milk"
2025-07-09 09:43:12.662 | DEBUG | modules.connect:on_decode   :927 - client_id:444 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:12.662 | DEBUG | server :receive     :332 - client_id:444 - 解码成功，发送结果: ancient cultures and tribes began to keep them for...
2025-07-09 09:43:12.662 | INFO  | modules.connect:on_result   :409 - client_id:444 - <<< [发送] 第3个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk"
2025-07-09 09:43:12.662 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:12.662 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:43:12.664 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 09:43:12.665 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:43:12.665 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-09 09:43:12.665 | DEBUG | modules.decoder:decode      :502 - client_id:555 - 第4个chunk, 原始帧: 384~485,  torch.Size([1, 101, 80])
2025-07-09 09:43:12.665 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:12.665 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:15.747 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:15.747 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:15.747 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:15.747 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - encoder 累计时点: 80
2025-07-09 09:43:15.748 | DEBUG | modules.decoder:decode_chunk:468 - client_id:555 - ctc 搜索起始时点: 64 encoder_lens: tensor([16])
2025-07-09 09:43:15.748 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:15.748 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:15.748 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:15.749 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:15.749 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:15.785 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:15.785 | DEBUG | modules.decoder:search      :359 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62), (1023, 65), (5246, 68), (4615, 69), (990, 71), (3152, 72), (2628, 73), (1659, 75), (5897, 76), (5343, 78)]
2025-07-09 09:43:15.785 | DEBUG | modules.decoder:decode_chunk:482 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the"
2025-07-09 09:43:15.785 | DEBUG | modules.connect:on_decode   :927 - client_id:555 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:15.785 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 09:43:15.786 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第4个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the"
2025-07-09 09:43:15.786 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:15.786 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:15.788 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 09:43:15.789 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:15.789 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-09 09:43:15.789 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:15.789 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:43:15.789 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:15.790 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:43:15.792 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 09:43:15.793 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:43:15.793 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-09 09:43:15.793 | DEBUG | modules.connect:on_decode   :931 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:15.793 | DEBUG | server :receive     :336 - client_id:111 - 解码成功，但无结果返回
2025-07-09 09:43:15.793 | DEBUG | server :receive     :302 - client_id:111 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:15.793 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:15.798 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 09:43:15.798 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:15.798 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-09 09:43:15.798 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:15.799 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:15.799 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:15.799 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:15.800 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 09:43:15.801 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:15.801 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-09 09:43:15.801 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:15.801 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:43:15.801 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:15.801 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:43:15.803 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 09:43:15.803 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:43:15.803 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-09 09:43:15.804 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:15.804 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:43:15.804 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:15.804 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:15.806 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-09 09:43:15.806 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:15.806 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-09 09:43:15.807 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:15.807 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:43:15.807 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:15.807 | DEBUG | server :receive     :312 - client_id:111 - 开始解析数据包
2025-07-09 09:43:15.809 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 534
2025-07-09 09:43:15.809 | DEBUG | server :receive     :321 - client_id:111 - 数据包解析成功，开始解码
2025-07-09 09:43:15.809 | DEBUG | modules.connect:on_decode   :911 - client_id:111 - 所需帧数: 67, 目前帧数: 534, 开始解码
2025-07-09 09:43:15.809 | DEBUG | modules.decoder:decode      :513 - client_id:111 - 第5个chunk, 原始帧: 480~534,  torch.Size([1, 54, 80])
2025-07-09 09:43:15.809 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:15.810 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:18.889 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:18.889 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:18.889 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:18.889 | DEBUG | modules.decoder:decode_chunk:467 - client_id:111 - encoder 累计时点: 88
2025-07-09 09:43:18.890 | DEBUG | modules.decoder:decode_chunk:468 - client_id:111 - ctc 搜索起始时点: 80 encoder_lens: tensor([8])
2025-07-09 09:43:18.890 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:18.890 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:18.891 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:18.891 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:18.891 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:18.930 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:18.930 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 12), (5204, 16), (3698, 20), (5343, 22), (3467, 24), (2833, 28), (3227, 30), (3698, 33), (4565, 36), (4615, 38), (223, 40), (1624, 44), (5343, 49), (3805, 52), (1811, 54), (3047, 57), (3698, 61), (5343, 63), (3467, 66), (2833, 70), (745, 74), (5343, 79), (1269, 82), (5026, 85)]
2025-07-09 09:43:18.930 | INFO  | modules.decoder:detokenize  :533 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 09:43:18.930 | DEBUG | modules.decoder:decode_chunk:482 - client_id:111 - ctc 累计搜索结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the crust"
2025-07-09 09:43:18.930 | INFO  | modules.connect:on_decode   :922 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-09 09:43:18.930 | DEBUG | server :receive     :332 - client_id:111 - 解码成功，发送结果: the surface of the moon is made of rocks and dust ...
2025-07-09 09:43:18.931 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第5个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the crust"
2025-07-09 09:43:18.931 | INFO  | server :receive     :343 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 09:43:18.931 | INFO  | server :receive     :349 - client_id: 111 - 关闭连接，清理资源
2025-07-09 09:43:18.931 | DEBUG | modules.connect:disconnect  :284 - client_id:111 - 开始断开连接，当前活跃连接数: 5
2025-07-09 09:43:18.931 | DEBUG | modules.connect:disconnect  :287 - client_id:111 - 关闭WebSocket连接
2025-07-09 09:43:18.931 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:18.934 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-09 09:43:18.934 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:18.934 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-09 09:43:18.934 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:18.935 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:18.935 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:18.935 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:18.937 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-09 09:43:18.937 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:18.937 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-09 09:43:18.937 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:18.938 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:43:18.938 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:18.938 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:43:18.940 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-09 09:43:18.940 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:43:18.940 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-09 09:43:18.940 | DEBUG | modules.connect:on_decode   :931 - client_id:555 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:18.940 | DEBUG | server :receive     :336 - client_id:555 - 解码成功，但无结果返回
2025-07-09 09:43:18.940 | DEBUG | server :receive     :302 - client_id:555 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:18.940 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:18.942 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-09 09:43:18.942 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:18.942 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-09 09:43:18.943 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第5个chunk, 原始帧: 480~581,  torch.Size([1, 101, 80])
2025-07-09 09:43:18.943 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:18.943 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:22.026 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:22.026 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:22.026 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:22.026 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 96
2025-07-09 09:43:22.027 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 80 encoder_lens: tensor([16])
2025-07-09 09:43:22.027 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:22.027 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:22.028 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:22.028 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:22.028 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:22.064 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:22.064 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28), (1609, 33), (4092, 38), (4615, 41), (5424, 46), (223, 50), (5348, 53), (5897, 57), (960, 64), (5804, 69), (5246, 72), (2434, 77), (5023, 92)]
2025-07-09 09:43:22.064 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two dry powers together and then with clean wet hands squeeze"
2025-07-09 09:43:22.064 | DEBUG | modules.connect:on_decode   :927 - client_id:222 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:22.064 | DEBUG | server :receive     :332 - client_id:222 - 解码成功，发送结果: swirl the two dry powers together and then with cl...
2025-07-09 09:43:22.064 | INFO  | modules.connect:on_result   :409 - client_id:222 - <<< [发送] 第4个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands squeeze"
2025-07-09 09:43:22.065 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:22.065 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:22.068 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-09 09:43:22.068 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:22.068 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-09 09:43:22.068 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第5个chunk, 原始帧: 480~581,  torch.Size([1, 101, 80])
2025-07-09 09:43:22.068 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:22.068 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:25.152 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:25.152 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:25.152 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:25.152 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 96
2025-07-09 09:43:25.153 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 80 encoder_lens: tensor([16])
2025-07-09 09:43:25.153 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:25.153 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:25.154 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:25.154 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:25.154 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:25.189 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:25.189 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75), (3736, 80), (2319, 84), (5748, 90), (755, 93), (3661, 95)]
2025-07-09 09:43:25.190 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the niraongo volcano"
2025-07-09 09:43:25.190 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:25.190 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb the niraongo vo...
2025-07-09 09:43:25.190 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第4个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano"
2025-07-09 09:43:25.190 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:25.190 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:25.193 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-09 09:43:25.193 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:25.193 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-09 09:43:25.193 | DEBUG | modules.decoder:decode      :502 - client_id:444 - 第5个chunk, 原始帧: 480~581,  torch.Size([1, 101, 80])
2025-07-09 09:43:25.193 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:25.193 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:28.277 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:28.277 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:28.277 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:28.278 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 96
2025-07-09 09:43:28.278 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 80 encoder_lens: tensor([16])
2025-07-09 09:43:28.278 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:28.278 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:28.279 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:28.279 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:28.279 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:28.315 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:28.315 | DEBUG | modules.decoder:search      :359 - ctc token&time [(215, 19), (923, 22), (1283, 25), (4615, 30), (223, 32), (5499, 35), (505, 44), (5422, 47), (2946, 50), (5346, 53), (2128, 56), (1650, 60), (37, 66), (5422, 73), (3393, 79), (2421, 86), (3323, 92), (369, 94)]
2025-07-09 09:43:28.315 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat"
2025-07-09 09:43:28.315 | DEBUG | modules.connect:on_decode   :927 - client_id:444 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:28.315 | DEBUG | server :receive     :332 - client_id:444 - 解码成功，发送结果: ancient cultures and tribes began to keep them for...
2025-07-09 09:43:28.315 | INFO  | modules.connect:on_result   :409 - client_id:444 - <<< [发送] 第4个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat"
2025-07-09 09:43:28.316 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:28.316 | DEBUG | server :receive     :312 - client_id:555 - 开始解析数据包
2025-07-09 09:43:28.322 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第15个数据包, 累计帧数: 586
2025-07-09 09:43:28.323 | DEBUG | server :receive     :321 - client_id:555 - 数据包解析成功，开始解码
2025-07-09 09:43:28.323 | DEBUG | modules.connect:on_decode   :911 - client_id:555 - 所需帧数: 67, 目前帧数: 586, 开始解码
2025-07-09 09:43:28.323 | DEBUG | modules.decoder:decode      :502 - client_id:555 - 第5个chunk, 原始帧: 480~581,  torch.Size([1, 101, 80])
2025-07-09 09:43:28.323 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:28.323 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:31.407 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:31.407 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:31.407 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:31.408 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - encoder 累计时点: 96
2025-07-09 09:43:31.408 | DEBUG | modules.decoder:decode_chunk:468 - client_id:555 - ctc 搜索起始时点: 80 encoder_lens: tensor([16])
2025-07-09 09:43:31.408 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:31.408 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:31.409 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:31.409 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:31.409 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:31.445 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:31.445 | DEBUG | modules.decoder:search      :359 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62), (1023, 65), (5246, 68), (4615, 69), (990, 71), (3152, 72), (2628, 73), (1659, 75), (5897, 76), (5343, 78), (1643, 80), (3176, 85), (137, 90)]
2025-07-09 09:43:31.445 | INFO  | modules.decoder:detokenize  :533 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 09:43:31.445 | DEBUG | modules.decoder:decode_chunk:482 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-09 09:43:31.446 | DEBUG | modules.decoder:decode      :513 - client_id:555 - 第6个chunk, 原始帧: 576~586,  torch.Size([1, 10, 80])
2025-07-09 09:43:31.446 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:31.446 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:34.451 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:34.452 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:34.452 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:34.452 | ERROR | modules.decoder:ort_run     :238 - encoder.onnx 请求错误! [ONNXRuntimeError] : 2 : INVALID_ARGUMENT : Non-zero status code returned while running Conv node. Name:'/embed/conv/conv.3/Relu_output_0_nchwc' Status Message: Invalid input shape: {4,39}
2025-07-09 09:43:34.453 | DEBUG | modules.decoder:decode_chunk:467 - client_id:555 - encoder 累计时点: 96
2025-07-09 09:43:34.454 | DEBUG | modules.decoder:decode_chunk:468 - client_id:555 - ctc 搜索起始时点: 96 encoder_lens: tensor([0])
2025-07-09 09:43:34.455 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:34.455 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:34.456 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:34.456 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:34.456 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:34.497 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:34.497 | DEBUG | modules.decoder:search      :359 - ctc token&time [(4683, 12), (2553, 18), (5422, 20), (5588, 25), (2576, 28), (4006, 32), (2141, 37), (1823, 43), (3214, 45), (2576, 47), (5343, 50), (1643, 53), (2144, 57), (4862, 62), (1023, 65), (5246, 68), (4615, 69), (990, 71), (3152, 72), (2628, 73), (1659, 75), (5897, 76), (5343, 78), (1643, 80), (3176, 85), (137, 90)]
2025-07-09 09:43:34.497 | INFO  | modules.decoder:detokenize  :533 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 09:43:34.497 | DEBUG | modules.decoder:decode_chunk:482 - client_id:555 - ctc 累计搜索结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-09 09:43:34.497 | INFO  | modules.connect:on_decode   :922 - client_id:555 - *** 最后一个数据包完成解码 ***
2025-07-09 09:43:34.498 | DEBUG | server :receive     :332 - client_id:555 - 解码成功，发送结果: scientists hope to understand how plane form espec...
2025-07-09 09:43:34.498 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第5个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-09 09:43:34.498 | INFO  | server :receive     :343 - client_id:555 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 09:43:34.498 | INFO  | server :receive     :349 - client_id: 555 - 关闭连接，清理资源
2025-07-09 09:43:34.498 | DEBUG | modules.connect:disconnect  :284 - client_id:555 - 开始断开连接，当前活跃连接数: 5
2025-07-09 09:43:34.498 | DEBUG | modules.connect:disconnect  :287 - client_id:555 - 关闭WebSocket连接
2025-07-09 09:43:34.499 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:34.507 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-09 09:43:34.507 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:34.507 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-09 09:43:34.507 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:34.508 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:43:34.508 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:34.509 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:34.511 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-09 09:43:34.511 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:34.511 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-09 09:43:34.511 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:34.511 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:34.511 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:34.512 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:34.514 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-09 09:43:34.515 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:34.515 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-09 09:43:34.515 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:34.515 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:43:34.515 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:34.515 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:34.517 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-09 09:43:34.518 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:34.518 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-09 09:43:34.518 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第6个chunk, 原始帧: 576~677,  torch.Size([1, 101, 80])
2025-07-09 09:43:34.518 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:34.518 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:37.601 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:37.601 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:37.601 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:37.602 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 112
2025-07-09 09:43:37.602 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 96 encoder_lens: tensor([16])
2025-07-09 09:43:37.602 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:37.602 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:37.603 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:37.603 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:37.603 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:37.638 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:37.638 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28), (1609, 33), (4092, 38), (4615, 41), (5424, 46), (223, 50), (5348, 53), (5897, 57), (960, 64), (5804, 69), (5246, 72), (2434, 77), (5023, 92), (5346, 97), (2801, 102), (6, 105), (452, 108), (2994, 111)]
2025-07-09 09:43:37.639 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two dry powers together and then with clean wet hands squeeze them into a balll"
2025-07-09 09:43:37.639 | DEBUG | modules.connect:on_decode   :927 - client_id:222 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:37.639 | DEBUG | server :receive     :332 - client_id:222 - 解码成功，发送结果: swirl the two dry powers together and then with cl...
2025-07-09 09:43:37.639 | INFO  | modules.connect:on_result   :409 - client_id:222 - <<< [发送] 第5个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands squeeze them into a balll"
2025-07-09 09:43:37.639 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:37.640 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:37.642 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-09 09:43:37.642 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:37.642 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-09 09:43:37.643 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第6个chunk, 原始帧: 576~677,  torch.Size([1, 101, 80])
2025-07-09 09:43:37.643 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:37.643 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:40.727 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:40.727 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:40.727 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:40.728 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 112
2025-07-09 09:43:40.728 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 96 encoder_lens: tensor([16])
2025-07-09 09:43:40.728 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:40.728 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:40.729 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:40.729 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:40.729 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:40.770 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:40.770 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75), (3736, 80), (2319, 84), (5748, 90), (755, 93), (3661, 95), (178, 102), (5897, 105), (4953, 108), (3698, 110)]
2025-07-09 09:43:40.770 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the niraongo volcano along with some of"
2025-07-09 09:43:40.770 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:40.770 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb the niraongo vo...
2025-07-09 09:43:40.770 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第5个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of"
2025-07-09 09:43:40.771 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:40.771 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:40.773 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-09 09:43:40.773 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:40.773 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-09 09:43:40.774 | DEBUG | modules.decoder:decode      :502 - client_id:444 - 第6个chunk, 原始帧: 576~677,  torch.Size([1, 101, 80])
2025-07-09 09:43:40.774 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:40.774 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:43.858 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:43.858 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:43.859 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:43.859 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 112
2025-07-09 09:43:43.859 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 96 encoder_lens: tensor([16])
2025-07-09 09:43:43.859 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:43.859 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:43.860 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:43.860 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:43.860 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:43.896 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:43.896 | DEBUG | modules.decoder:search      :359 - ctc token&time [(215, 19), (923, 22), (1283, 25), (4615, 30), (223, 32), (5499, 35), (505, 44), (5422, 47), (2946, 50), (5346, 53), (2128, 56), (1650, 60), (37, 66), (5422, 73), (3393, 79), (2421, 86), (3323, 92), (369, 94), (223, 98), (4884, 102), (4615, 106)]
2025-07-09 09:43:43.896 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat and skins"
2025-07-09 09:43:43.896 | DEBUG | modules.connect:on_decode   :927 - client_id:444 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:43.896 | DEBUG | server :receive     :332 - client_id:444 - 解码成功，发送结果: ancient cultures and tribes began to keep them for...
2025-07-09 09:43:43.896 | INFO  | modules.connect:on_result   :409 - client_id:444 - <<< [发送] 第5个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat and skins"
2025-07-09 09:43:43.897 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:43.897 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:43.899 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-09 09:43:43.899 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:43.899 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-09 09:43:43.900 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:43.900 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:43:43.900 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:43.900 | DEBUG | modules.connect:disconnect  :290 - client_id:111 - WebSocket连接已关闭
2025-07-09 09:43:43.900 | DEBUG | modules.connect:disconnect  :298 - client_id:111 - 已从active_connection中移除
2025-07-09 09:43:43.900 | DEBUG | modules.connect:disconnect  :304 - client_id:111 - 开始清理客户端状态和缓存
2025-07-09 09:43:43.900 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:43:43.900 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:43:43.900 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:43:43.901 | DEBUG | modules.connect:disconnect  :317 - client_id:111 - 客户端状态已完全清理
2025-07-09 09:43:43.901 | DEBUG | modules.connect:disconnect  :324 - client_id:111 - 活跃连接数已更新: 4
2025-07-09 09:43:43.901 | DEBUG | server :websocket_endpoint:258 - client_id:111 - 协程完成，清理待处理任务
2025-07-09 09:43:43.902 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:43.904 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-09 09:43:43.904 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:43.904 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-09 09:43:43.904 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:43.904 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:43.904 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:43.905 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:43.906 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-09 09:43:43.907 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:43.907 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-09 09:43:43.907 | DEBUG | modules.connect:on_decode   :931 - client_id:444 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:43.907 | DEBUG | server :receive     :336 - client_id:444 - 解码成功，但无结果返回
2025-07-09 09:43:43.907 | DEBUG | server :receive     :302 - client_id:444 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:43.907 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:43.909 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-09 09:43:43.909 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:43.909 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 760, 开始解码
2025-07-09 09:43:43.910 | DEBUG | modules.connect:on_decode   :931 - client_id:222 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:43.910 | DEBUG | server :receive     :336 - client_id:222 - 解码成功，但无结果返回
2025-07-09 09:43:43.910 | DEBUG | server :receive     :302 - client_id:222 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:43.910 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:43.912 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-09 09:43:43.912 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:43.913 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 760, 开始解码
2025-07-09 09:43:43.913 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:43.913 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:43.913 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:43.913 | DEBUG | server :receive     :312 - client_id:444 - 开始解析数据包
2025-07-09 09:43:43.918 | INFO  | modules.connect:on_check    :600 - client_id:444 - >>> [解析] 第19个数据包, 累计帧数: 740
2025-07-09 09:43:43.919 | DEBUG | server :receive     :321 - client_id:444 - 数据包解析成功，开始解码
2025-07-09 09:43:43.919 | DEBUG | modules.connect:on_decode   :911 - client_id:444 - 所需帧数: 67, 目前帧数: 740, 开始解码
2025-07-09 09:43:43.919 | DEBUG | modules.decoder:decode      :513 - client_id:444 - 第7个chunk, 原始帧: 672~740,  torch.Size([1, 68, 80])
2025-07-09 09:43:43.919 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:43.919 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:47.000 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:47.000 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:47.001 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:47.001 | DEBUG | modules.decoder:decode_chunk:467 - client_id:444 - encoder 累计时点: 122
2025-07-09 09:43:47.001 | DEBUG | modules.decoder:decode_chunk:468 - client_id:444 - ctc 搜索起始时点: 112 encoder_lens: tensor([10])
2025-07-09 09:43:47.001 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:47.001 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:47.002 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:47.002 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:47.002 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:47.032 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:47.032 | DEBUG | modules.decoder:search      :359 - ctc token&time [(215, 19), (923, 22), (1283, 25), (4615, 30), (223, 32), (5499, 35), (505, 44), (5422, 47), (2946, 50), (5346, 53), (2128, 56), (1650, 60), (37, 66), (5422, 73), (3393, 79), (2421, 86), (3323, 92), (369, 94), (223, 98), (4884, 102), (4615, 106)]
2025-07-09 09:43:47.032 | INFO  | modules.decoder:detokenize  :533 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 09:43:47.032 | DEBUG | modules.decoder:decode_chunk:482 - client_id:444 - ctc 累计搜索结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat and skins"
2025-07-09 09:43:47.032 | INFO  | modules.connect:on_decode   :922 - client_id:444 - *** 最后一个数据包完成解码 ***
2025-07-09 09:43:47.032 | DEBUG | server :receive     :332 - client_id:444 - 解码成功，发送结果: ancient cultures and tribes began to keep them for...
2025-07-09 09:43:47.032 | INFO  | modules.connect:on_result   :409 - client_id:444 - <<< [发送] 第6个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat and skins"
2025-07-09 09:43:47.033 | INFO  | server :receive     :343 - client_id:444 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 09:43:47.033 | INFO  | server :receive     :349 - client_id: 444 - 关闭连接，清理资源
2025-07-09 09:43:47.033 | DEBUG | modules.connect:disconnect  :284 - client_id:444 - 开始断开连接，当前活跃连接数: 4
2025-07-09 09:43:47.033 | DEBUG | modules.connect:disconnect  :287 - client_id:444 - 关闭WebSocket连接
2025-07-09 09:43:47.033 | DEBUG | server :receive     :312 - client_id:222 - 开始解析数据包
2025-07-09 09:43:47.036 | INFO  | modules.connect:on_check    :600 - client_id:222 - >>> [解析] 第20个数据包, 累计帧数: 794
2025-07-09 09:43:47.036 | DEBUG | server :receive     :321 - client_id:222 - 数据包解析成功，开始解码
2025-07-09 09:43:47.036 | DEBUG | modules.connect:on_decode   :911 - client_id:222 - 所需帧数: 67, 目前帧数: 794, 开始解码
2025-07-09 09:43:47.036 | DEBUG | modules.decoder:decode      :502 - client_id:222 - 第7个chunk, 原始帧: 672~773,  torch.Size([1, 101, 80])
2025-07-09 09:43:47.036 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:47.036 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:50.119 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:50.119 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:50.119 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:50.120 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 128
2025-07-09 09:43:50.120 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 112 encoder_lens: tensor([16])
2025-07-09 09:43:50.120 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:50.120 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:50.121 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:50.121 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:50.121 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:50.157 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:50.157 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28), (1609, 33), (4092, 38), (4615, 41), (5424, 46), (223, 50), (5348, 53), (5897, 57), (960, 64), (5804, 69), (5246, 72), (2434, 77), (5023, 92), (5346, 97), (2801, 102), (6, 105), (452, 108), (2994, 111)]
2025-07-09 09:43:50.157 | INFO  | modules.decoder:detokenize  :533 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 09:43:50.157 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two dry powers together and then with clean wet hands squeeze them into a balll"
2025-07-09 09:43:50.157 | DEBUG | modules.decoder:decode      :513 - client_id:222 - 第8个chunk, 原始帧: 768~794,  torch.Size([1, 26, 80])
2025-07-09 09:43:50.158 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:50.158 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:53.231 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:53.232 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:53.232 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:53.232 | DEBUG | modules.decoder:decode_chunk:467 - client_id:222 - encoder 累计时点: 131
2025-07-09 09:43:53.232 | DEBUG | modules.decoder:decode_chunk:468 - client_id:222 - ctc 搜索起始时点: 128 encoder_lens: tensor([3])
2025-07-09 09:43:53.232 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:53.233 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:53.233 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:53.233 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:53.233 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:53.254 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:53.254 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5223, 20), (2826, 22), (2994, 24), (5343, 26), (5544, 28), (1609, 33), (4092, 38), (4615, 41), (5424, 46), (223, 50), (5348, 53), (5897, 57), (960, 64), (5804, 69), (5246, 72), (2434, 77), (5023, 92), (5346, 97), (2801, 102), (6, 105), (452, 108), (2994, 111)]
2025-07-09 09:43:53.254 | INFO  | modules.decoder:detokenize  :533 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 09:43:53.255 | DEBUG | modules.decoder:decode_chunk:482 - client_id:222 - ctc 累计搜索结果: "swirl the two dry powers together and then with clean wet hands squeeze them into a balll"
2025-07-09 09:43:53.255 | INFO  | modules.connect:on_decode   :922 - client_id:222 - *** 最后一个数据包完成解码 ***
2025-07-09 09:43:53.255 | DEBUG | server :receive     :332 - client_id:222 - 解码成功，发送结果: swirl the two dry powers together and then with cl...
2025-07-09 09:43:53.255 | INFO  | modules.connect:on_result   :409 - client_id:222 - <<< [发送] 第6个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands squeeze them into a balll"
2025-07-09 09:43:53.255 | INFO  | server :receive     :343 - client_id:222 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 09:43:53.255 | INFO  | server :receive     :349 - client_id: 222 - 关闭连接，清理资源
2025-07-09 09:43:53.255 | DEBUG | modules.connect:disconnect  :284 - client_id:222 - 开始断开连接，当前活跃连接数: 4
2025-07-09 09:43:53.255 | DEBUG | modules.connect:disconnect  :287 - client_id:222 - 关闭WebSocket连接
2025-07-09 09:43:53.256 | DEBUG | modules.connect:disconnect  :290 - client_id:555 - WebSocket连接已关闭
2025-07-09 09:43:53.256 | DEBUG | modules.connect:disconnect  :298 - client_id:555 - 已从active_connection中移除
2025-07-09 09:43:53.256 | DEBUG | modules.connect:disconnect  :304 - client_id:555 - 开始清理客户端状态和缓存
2025-07-09 09:43:53.256 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:43:53.256 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:43:53.256 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:43:53.256 | DEBUG | modules.connect:disconnect  :317 - client_id:555 - 客户端状态已完全清理
2025-07-09 09:43:53.256 | DEBUG | modules.connect:disconnect  :324 - client_id:555 - 活跃连接数已更新: 3
2025-07-09 09:43:53.257 | DEBUG | server :websocket_endpoint:258 - client_id:555 - 协程完成，清理待处理任务
2025-07-09 09:43:53.257 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:53.261 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-07-09 09:43:53.261 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:53.261 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 798, 开始解码
2025-07-09 09:43:53.262 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第7个chunk, 原始帧: 672~773,  torch.Size([1, 101, 80])
2025-07-09 09:43:53.262 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:53.262 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:56.346 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:56.346 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:56.346 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:56.346 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 128
2025-07-09 09:43:56.347 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 112 encoder_lens: tensor([16])
2025-07-09 09:43:56.347 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:56.347 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:56.348 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:56.348 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:56.348 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:56.384 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:56.384 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75), (3736, 80), (2319, 84), (5748, 90), (755, 93), (3661, 95), (178, 102), (5897, 105), (4953, 108), (3698, 110), (5343, 112), (877, 115), (1826, 119), (3489, 125)]
2025-07-09 09:43:56.384 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain"
2025-07-09 09:43:56.384 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:56.384 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb the niraongo vo...
2025-07-09 09:43:56.385 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第6个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain"
2025-07-09 09:43:56.385 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:56.385 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:56.388 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-07-09 09:43:56.388 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:56.388 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 836, 开始解码
2025-07-09 09:43:56.388 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:56.388 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:56.389 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:56.389 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:56.396 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-07-09 09:43:56.396 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:56.396 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 874, 开始解码
2025-07-09 09:43:56.396 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第8个chunk, 原始帧: 768~869,  torch.Size([1, 101, 80])
2025-07-09 09:43:56.397 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:56.397 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:43:59.479 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:43:59.479 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:43:59.479 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:43:59.480 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 144
2025-07-09 09:43:59.480 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 128 encoder_lens: tensor([16])
2025-07-09 09:43:59.480 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:43:59.480 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:43:59.481 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:43:59.481 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:43:59.481 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:43:59.517 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:43:59.517 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75), (3736, 80), (2319, 84), (5748, 90), (755, 93), (3661, 95), (178, 102), (5897, 105), (4953, 108), (3698, 110), (5343, 112), (877, 115), (1826, 119), (3489, 125), (2320, 130), (1811, 132), (4519, 133), (3152, 134), (5, 135), (5466, 138), (2936, 142)]
2025-07-09 09:43:59.517 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla traffick"
2025-07-09 09:43:59.517 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:43:59.517 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb the niraongo vo...
2025-07-09 09:43:59.517 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第7个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla traffick"
2025-07-09 09:43:59.518 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:59.518 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:59.520 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-07-09 09:43:59.521 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:59.521 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 912, 开始解码
2025-07-09 09:43:59.521 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:59.521 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:59.521 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:59.521 | DEBUG | modules.connect:disconnect  :290 - client_id:444 - WebSocket连接已关闭
2025-07-09 09:43:59.521 | DEBUG | modules.connect:disconnect  :298 - client_id:444 - 已从active_connection中移除
2025-07-09 09:43:59.522 | DEBUG | modules.connect:disconnect  :304 - client_id:444 - 开始清理客户端状态和缓存
2025-07-09 09:43:59.522 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:43:59.522 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:43:59.522 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:43:59.522 | DEBUG | modules.connect:disconnect  :317 - client_id:444 - 客户端状态已完全清理
2025-07-09 09:43:59.522 | DEBUG | modules.connect:disconnect  :324 - client_id:444 - 活跃连接数已更新: 2
2025-07-09 09:43:59.522 | DEBUG | modules.connect:disconnect  :290 - client_id:222 - WebSocket连接已关闭
2025-07-09 09:43:59.522 | DEBUG | modules.connect:disconnect  :298 - client_id:222 - 已从active_connection中移除
2025-07-09 09:43:59.522 | DEBUG | modules.connect:disconnect  :304 - client_id:222 - 开始清理客户端状态和缓存
2025-07-09 09:43:59.522 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:43:59.523 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:43:59.523 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:43:59.523 | DEBUG | modules.connect:disconnect  :317 - client_id:222 - 客户端状态已完全清理
2025-07-09 09:43:59.523 | DEBUG | modules.connect:disconnect  :324 - client_id:222 - 活跃连接数已更新: 1
2025-07-09 09:43:59.523 | DEBUG | server :websocket_endpoint:258 - client_id:444 - 协程完成，清理待处理任务
2025-07-09 09:43:59.523 | DEBUG | server :websocket_endpoint:258 - client_id:222 - 协程完成，清理待处理任务
2025-07-09 09:43:59.523 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:59.526 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-07-09 09:43:59.527 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:59.527 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 950, 开始解码
2025-07-09 09:43:59.527 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:43:59.527 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:43:59.527 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:43:59.527 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:43:59.529 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-07-09 09:43:59.529 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:43:59.530 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 988, 开始解码
2025-07-09 09:43:59.530 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第9个chunk, 原始帧: 864~965,  torch.Size([1, 101, 80])
2025-07-09 09:43:59.530 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:43:59.530 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:44:02.614 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:44:02.614 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:44:02.614 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:44:02.615 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 160
2025-07-09 09:44:02.616 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 144 encoder_lens: tensor([16])
2025-07-09 09:44:02.616 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:44:02.616 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:44:02.616 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:44:02.617 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:44:02.617 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:44:02.652 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:44:02.652 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75), (3736, 80), (2319, 84), (5748, 90), (755, 93), (3661, 95), (178, 102), (5897, 105), (4953, 108), (3698, 110), (5343, 112), (877, 115), (1826, 119), (3489, 125), (2320, 130), (1811, 132), (4519, 133), (3152, 134), (5, 135), (5466, 138), (2936, 142), (2742, 144), (2699, 147), (120, 153)]
2025-07-09 09:44:02.652 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla trafficking in africa"
2025-07-09 09:44:02.652 | DEBUG | modules.connect:on_decode   :927 - client_id:333 - chunk 完成解码, 更新识别结果
2025-07-09 09:44:02.652 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb the niraongo vo...
2025-07-09 09:44:02.652 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第8个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla trafficking in africa"
2025-07-09 09:44:02.652 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:44:02.653 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:44:02.655 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-07-09 09:44:02.655 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:44:02.655 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 1026, 开始解码
2025-07-09 09:44:02.656 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:44:02.656 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:44:02.656 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:44:02.656 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:44:02.658 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-07-09 09:44:02.658 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:44:02.658 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 1064, 开始解码
2025-07-09 09:44:02.658 | DEBUG | modules.decoder:decode      :502 - client_id:333 - 第10个chunk, 原始帧: 960~1061,  torch.Size([1, 101, 80])
2025-07-09 09:44:02.659 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:44:02.659 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:44:05.747 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:44:05.748 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:44:05.748 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:44:05.748 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 176
2025-07-09 09:44:05.748 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 160 encoder_lens: tensor([16])
2025-07-09 09:44:05.749 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:44:05.749 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:44:05.749 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:44:05.750 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:44:05.750 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:44:05.785 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:44:05.785 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75), (3736, 80), (2319, 84), (5748, 90), (755, 93), (3661, 95), (178, 102), (5897, 105), (4953, 108), (3698, 110), (5343, 112), (877, 115), (1826, 119), (3489, 125), (2320, 130), (1811, 132), (4519, 133), (3152, 134), (5, 135), (5466, 138), (2936, 142), (2742, 144), (2699, 147), (120, 153)]
2025-07-09 09:44:05.785 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla trafficking in africa"
2025-07-09 09:44:05.786 | DEBUG | modules.connect:on_decode   :931 - client_id:333 - chunk 完成解码, 识别结果不变
2025-07-09 09:44:05.786 | DEBUG | server :receive     :336 - client_id:333 - 解码成功，但无结果返回
2025-07-09 09:44:05.786 | DEBUG | server :receive     :302 - client_id:333 - 等待接收数据包，超时时间: 3000秒
2025-07-09 09:44:05.786 | DEBUG | server :receive     :312 - client_id:333 - 开始解析数据包
2025-07-09 09:44:05.788 | INFO  | modules.connect:on_check    :600 - client_id:333 - >>> [解析] 第28个数据包, 累计帧数: 1070
2025-07-09 09:44:05.788 | DEBUG | server :receive     :321 - client_id:333 - 数据包解析成功，开始解码
2025-07-09 09:44:05.788 | DEBUG | modules.connect:on_decode   :911 - client_id:333 - 所需帧数: 67, 目前帧数: 1070, 开始解码
2025-07-09 09:44:05.788 | DEBUG | modules.decoder:decode      :513 - client_id:333 - 第11个chunk, 原始帧: 1056~1070,  torch.Size([1, 14, 80])
2025-07-09 09:44:05.789 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_encoder - 05d65daa
2025-07-09 09:44:05.789 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 05d65daa
2025-07-09 09:44:08.852 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_encoder - 05d65daa, 当前可用: 1/1
2025-07-09 09:44:08.852 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 05d65daa 已加入等待队列
2025-07-09 09:44:08.852 | INFO  | modules.decoder:ort_run     :224 - 释放会话: en_encoder - 05d65daa
2025-07-09 09:44:08.853 | DEBUG | modules.decoder:decode_chunk:467 - client_id:333 - encoder 累计时点: 177
2025-07-09 09:44:08.853 | DEBUG | modules.decoder:decode_chunk:468 - client_id:333 - ctc 搜索起始时点: 176 encoder_lens: tensor([1])
2025-07-09 09:44:08.853 | DEBUG | modules.onnx_session_pool:get_session :186 - 获取现有会话: en_ctc - 350bbaf3
2025-07-09 09:44:08.854 | INFO  | modules.decoder:ort_run     :290 - 获取会话: en_ctc - 350bbaf3
2025-07-09 09:44:08.854 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: en_ctc - 350bbaf3, 当前可用: 1/1
2025-07-09 09:44:08.854 | DEBUG | modules.onnx_session_pool:release_session:269 - 会话 350bbaf3 已加入等待队列
2025-07-09 09:44:08.854 | INFO  | modules.decoder:ort_run     :298 - 释放会话: en_ctc - 350bbaf3
2025-07-09 09:44:08.890 | DEBUG | modules.decoder:search      :358 - ctc 前缀集束搜索完成
2025-07-09 09:44:08.890 | DEBUG | modules.decoder:search      :359 - ctc token&time [(5343, 28), (942, 32), (2833, 37), (184, 41), (5343, 46), (475, 50), (5422, 55), (971, 58), (5343, 64), (3602, 70), (4291, 75), (3736, 80), (2319, 84), (5748, 90), (755, 93), (3661, 95), (178, 102), (5897, 105), (4953, 108), (3698, 110), (5343, 112), (877, 115), (1826, 119), (3489, 125), (2320, 130), (1811, 132), (4519, 133), (3152, 134), (5, 135), (5466, 138), (2936, 142), (2742, 144), (2699, 147), (120, 153)]
2025-07-09 09:44:08.890 | INFO  | modules.decoder:detokenize  :533 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 09:44:08.890 | DEBUG | modules.decoder:decode_chunk:482 - client_id:333 - ctc 累计搜索结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla trafficking in africa"
2025-07-09 09:44:08.890 | INFO  | modules.connect:on_decode   :922 - client_id:333 - *** 最后一个数据包完成解码 ***
2025-07-09 09:44:08.890 | DEBUG | server :receive     :332 - client_id:333 - 解码成功，发送结果: the city is also the base to climb the niraongo vo...
2025-07-09 09:44:08.891 | INFO  | modules.connect:on_result   :409 - client_id:333 - <<< [发送] 第9个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla trafficking in africa"
2025-07-09 09:44:08.891 | INFO  | server :receive     :343 - client_id:333 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 09:44:08.891 | INFO  | server :receive     :349 - client_id: 333 - 关闭连接，清理资源
2025-07-09 09:44:08.891 | DEBUG | modules.connect:disconnect  :284 - client_id:333 - 开始断开连接，当前活跃连接数: 1
2025-07-09 09:44:08.891 | DEBUG | modules.connect:disconnect  :287 - client_id:333 - 关闭WebSocket连接
2025-07-09 09:44:08.893 | DEBUG | modules.connect:disconnect  :290 - client_id:333 - WebSocket连接已关闭
2025-07-09 09:44:08.893 | DEBUG | modules.connect:disconnect  :298 - client_id:333 - 已从active_connection中移除
2025-07-09 09:44:08.893 | DEBUG | modules.connect:disconnect  :304 - client_id:333 - 开始清理客户端状态和缓存
2025-07-09 09:44:08.893 | INFO  | modules.decoder:__del__     :428 - ASRDecoder 显式释放资源
2025-07-09 09:44:08.893 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 09:44:08.893 | INFO  | modules.decoder:__del__     :270 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 09:44:08.893 | DEBUG | modules.connect:disconnect  :317 - client_id:333 - 客户端状态已完全清理
2025-07-09 09:44:08.893 | DEBUG | modules.connect:disconnect  :324 - client_id:333 - 活跃连接数已更新: 0
2025-07-09 09:44:08.893 | DEBUG | server :websocket_endpoint:258 - client_id:333 - 协程完成，清理待处理任务
2025-07-09 09:44:34.556 | INFO  | server :lifespan    :124 - 正在关闭ASR服务...
2025-07-09 09:44:38.967 | INFO  | modules.monitoring:shutdown    :387 - 系统监控器已关闭
2025-07-09 09:44:38.967 | INFO  | server :lifespan    :131 - 系统监控已关闭
2025-07-09 09:44:43.968 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_encoder 的会话 a924462e, 使用次数: 0
2025-07-09 09:44:43.969 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_ctc 的会话 0a6ade5e, 使用次数: 0
2025-07-09 09:44:43.969 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 en_encoder 的会话 05d65daa, 使用次数: 42
2025-07-09 09:44:43.969 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 en_ctc 的会话 350bbaf3, 使用次数: 42
2025-07-09 09:44:43.969 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 ru_encoder 的会话 d78a2a98, 使用次数: 0
2025-07-09 09:44:43.970 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 ru_ctc 的会话 00ad4dcc, 使用次数: 0
2025-07-09 09:44:43.970 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 kk_encoder 的会话 cd488992, 使用次数: 0
2025-07-09 09:44:43.970 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 kk_ctc 的会话 8920895b, 使用次数: 0
2025-07-09 09:44:43.970 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 kkin_encoder 的会话 7a013662, 使用次数: 0
2025-07-09 09:44:43.971 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 kkin_ctc 的会话 8bb425ac, 使用次数: 0
2025-07-09 09:44:43.971 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 ug_encoder 的会话 f920ad35, 使用次数: 0
2025-07-09 09:44:43.971 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 ug_ctc 的会话 bb364064, 使用次数: 0
2025-07-09 09:44:44.138 | INFO  | modules.onnx_session_pool:shutdown    :381 - ONNX会话池已关闭
2025-07-09 09:44:44.138 | INFO  | server :lifespan    :138 - ONNX会话池已关闭
2025-07-09 09:44:44.138 | INFO  | modules.asr_manager:cleanup     :242 - 清理ASR管理器资源
2025-07-09 09:44:44.146 | INFO  | server :lifespan    :148 - Server shutdown, delete all global resources
