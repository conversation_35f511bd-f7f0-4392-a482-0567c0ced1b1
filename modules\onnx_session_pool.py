#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX会话池管理器
解决多个WebSocket连接并发访问ONNX模型时的性能问题
"""

import queue
import threading
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
import uuid
import onnxruntime as ort

from modules.error_codes import ErrorCode
from modules.logger import logger


@dataclass
class SessionInfo:
    """会话信息"""
    uid: str
    session: ort.InferenceSession
    input_names: List[str]
    metadata: Dict
    created_time: float
    last_used_time: float
    in_use: bool = False
    use_count: int = 0


class ONNXSessionPool:
    """ONNX会话池管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化会话池
        Args:
            config: 会话池配置
        """
        self.max_sessions_per_model = config.get('max_sessions_per_model', 4)
        self.session_timeout = config.get('session_timeout', 300)
        
        # 会话池存储 {model_name: [SessionInfo, ...]}
        self.session_pools: Dict[str, List[SessionInfo]] = {}
        # 等待队列 {model_name: queue.Queue}
        self.waiting_queues: Dict[str, queue.Queue] = {}
        # 线程锁
        self.locks: Dict[str, threading.RLock] = {}

        self.session_factories: Dict[str, Any] = {}
        
        # 清理线程
        self.cleanup_thread = None
        self.cleanup_interval = 60  # 清理间隔（秒）
        self.running = True
        
        self._start_cleanup_thread()
        logger.info(f"ONNX会话池已启用, 每个模型最大会话数: {self.max_sessions_per_model}")
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        self.cleanup_thread = threading.Thread(target=self._cleanup_expired_sessions, daemon=True)
        self.cleanup_thread.start()
    
    def _cleanup_expired_sessions(self):
        """清理过期会话"""
        while self.running:
            try:
                current_time = time.time()
                for model_name in list(self.session_pools.keys()):
                    with self.locks.get(model_name, threading.RLock()):
                        sessions = self.session_pools.get(model_name, [])
                        expired_sessions_indices = []   # 存储索引
                        
                        # 获取过期会话
                        for i, session_info in enumerate(sessions):
                            if (not session_info.in_use and 
                                current_time - session_info.last_used_time > self.session_timeout):
                                expired_sessions_indices.append(i)
                        
                        # 移除过期会话（保留至少一个会话）
                        if len(sessions) > 1:
                            for i in reversed(expired_sessions_indices):
                                expired_session = sessions.pop(i)
                                logger.debug(f"清理过期ONNX会话: {model_name}, 使用次数: {expired_session.use_count}")
                
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"清理过期会话时出错: {e}")
                time.sleep(self.cleanup_interval)
    
    def register_model(self, model_name: str, session_factory_func, *args, **kwargs):
        """
        注册模型到会话池 
        Args:
            model_name: 模型名称
            session_factory_func: 创建会话的工厂函数
            *args, **kwargs: 传递给工厂函数的参数
        """
        
        if model_name not in self.session_pools:
            self.session_pools[model_name] = []
            self.waiting_queues[model_name] = queue.Queue()
            self.locks[model_name] = threading.RLock()
            self.session_factories[model_name] = (session_factory_func, args, kwargs)
            
            # 预创建一个会话
            try:
                session, input_names, metadata = session_factory_func(*args, **kwargs)
                session_info = SessionInfo(
                    uid=str(uuid.uuid4())[:8],
                    session=session,
                    input_names=input_names,
                    metadata=metadata,
                    created_time=time.time(),
                    last_used_time=time.time()
                )
                self.session_pools[model_name].append(session_info)   # 添加一个session
            except Exception as e:
                logger.error(f"为模型 {model_name} 预创建会话失败: {e}")
    
    def get_session(self, model_name: str, timeout: float = 0.001) -> Optional[SessionInfo]:
        """
        获取会话
        Args:
            model_name: 模型名称
            timeout: 超时时间（秒）
        Returns:
            SessionInfo或None
        """
        if model_name not in self.session_pools:
            return None
        
        start_time = time.time()
        remaining_timeout = timeout

        while remaining_timeout > 0:
            with self.locks[model_name]:
                sessions = self.session_pools[model_name]
                
                # 1. 查找可用会话
                for session_info in sessions:
                    if not session_info.in_use:
                        session_info.in_use = True
                        session_info.last_used_time = time.time()
                        session_info.use_count += 1
                        return session_info
                
                # 2. 如果没有可用会话且未达到最大数量, 创建新会话
                if len(sessions) < self.max_sessions_per_model:
                    try:
                        factory_func, args, kwargs = self.session_factories[model_name]
                        session, input_names = factory_func(*args, **kwargs)
                        session_info = SessionInfo(
                            uid=str(uuid.uuid4())[:8],
                            session=session, 
                            input_names=input_names, 
                            metadata=metadata,
                            created_time=time.time(), 
                            last_used_time=time.time()
                        )
                        self.session_pools[model_name].append(session_info)
                        session_info.in_use = True   # 新创建的会话直接设为使用中
                        session_info.use_count += 1
                        logger.info(f"为模型 {model_name} 动态创建新会话,当前池大小: {len(self.session_pools[model_name])}")
                        return session_info
                    except Exception as e:
                        logger.error(f"为模型 {model_name} 动态创建会话失败: {e}")
                        return None

            # 3. 如果池已满且没有可用会话,从等待队列中获取
            try:
                # 使用队列等待, 而不是忙等
                session_info = self.waiting_queues[model_name].get(timeout=remaining_timeout)
                with self.locks[model_name]:
                    # 再次加锁确保在设置 in_use 时线程安全
                    session_info.in_use = True
                    session_info.last_used_time = time.time()
                    session_info.use_count += 1
                    return session_info
            except queue.Empty:
                # 超时未获取到会话
                pass

            # 更新剩余超时时间(在等待队列超时后)
            remaining_timeout = timeout - (time.time() - start_time)

        # 保底机制：如果超时仍未获取到会话，创建紧急会话
        logger.warning(f"获取模型 {model_name} 的会话超时，创建紧急会话")
        return self._create_emergency_session(model_name)
    
    def release_session(self, model_name: str, session_info: SessionInfo):
        """
        释放会话
        Args:
            model_name: 模型名称
            session_info: 会话信息
        """
        # 检查是否是紧急会话（不在池中的会话）
        if model_name not in self.session_pools:
            logger.debug(f"释放紧急会话: {model_name}")
            # 紧急会话直接丢弃，不加入池中
            return

        with self.locks[model_name]:
            # 检查会话是否在池中
            if session_info not in self.session_pools[model_name]:
                logger.debug(f"释放紧急会话（不在池中）: {model_name}")
                # 紧急会话直接丢弃
                return

            session_info.in_use = False
            session_info.last_used_time = time.time()

            # 通知等待的线程
            try:
                self.waiting_queues[model_name].put_nowait(session_info)
            except queue.Full:
                pass  # 队列满了就忽略

    def _create_emergency_session(self, model_name: str) -> Optional[SessionInfo]:
        """
        创建紧急会话（保底机制）

        Args:
            model_name: 模型名称

        Returns:
            SessionInfo或None
        """
        try:
            if model_name not in self.session_factories:
                logger.error(f"模型 {model_name} 未注册，无法创建紧急会话")
                return None

            factory_func, args, kwargs = self.session_factories[model_name]
            session, input_names = factory_func(*args, **kwargs)

            session_info = SessionInfo(
                uid=str(uuid.uuid4())[:8],
                session=session,
                input_names=input_names,
                metadata=metadata,
                created_time=time.time(),
                last_used_time=time.time(),
                in_use=True,
                use_count=1
            )

            logger.warning(f"为模型 {model_name} 创建紧急会话")
            return session_info

        except Exception as e:
            logger.error(f"创建紧急会话失败: {e}")
            return None
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取会话池统计信息"""
        stats = {}
        for model_name in self.session_pools:
            with self.locks[model_name]:
                sessions = self.session_pools[model_name]
                in_use_count = sum(1 for s in sessions if s.in_use)
                total_use_count = sum(s.use_count for s in sessions)
                
                stats[model_name] = {
                    'total_sessions': len(sessions),
                    'in_use_sessions': in_use_count,
                    'available_sessions': len(sessions) - in_use_count,
                    'total_use_count': total_use_count,
                    'waiting_queue_size': self.waiting_queues[model_name].qsize()
                }
        
        return stats
    
    def shutdown(self):
        """关闭会话池"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
        
        # 清理所有会话
        for model_name in self.session_pools:
            with self.locks[model_name]:
                sessions = self.session_pools[model_name]
                for session_info in sessions:
                    try:
                        # ONNX会话会自动清理, 这里只是记录
                        logger.debug(f"关闭模型 {model_name} 的会话, 使用次数: {session_info.use_count}")
                    except Exception as e:
                        logger.error(f"关闭会话时出错: {e}")
        
        self.session_pools.clear()
        self.waiting_queues.clear()
        self.locks.clear()
        
        logger.info("ONNX会话池已关闭")


# 全局会话池实例
_global_session_pool: Optional[ONNXSessionPool] = None


def get_global_session_pool() -> Optional[ONNXSessionPool]:
    """获取全局会话池实例"""
    return _global_session_pool


def initialize_global_session_pool(config: Dict[str, Any]):
    """初始化全局会话池"""
    global _global_session_pool
    if _global_session_pool is None:
        _global_session_pool = ONNXSessionPool(config)
    return _global_session_pool


def shutdown_global_session_pool():
    """关闭全局会话池"""
    global _global_session_pool
    if _global_session_pool:
        _global_session_pool.shutdown()
        _global_session_pool = None
