import base64
import os
from datetime import datetime

from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.serialization import load_pem_public_key


def load_public_key(file):
    if not os.path.exists(file):
        print(f"缺少公钥文件：{file}, 请联系服务方提供该文件。 ")
        exit(1)
    with open(file, "rb") as f:
        public_key = load_pem_public_key(f.read())
    return public_key

def verify_license_(license_key, public_key):
    try:
        # 解密
        decoded_license_key = base64.b64decode(license_key.strip())
        customer_id, expiration_date, signature = decoded_license_key.split(b"|", 2)
        customer_id = customer_id.decode()
        expiration_date = expiration_date.decode()
        auth_data = f"{customer_id}|{expiration_date}"

        # 1. 验证授权码的有效期
        expiration_date = datetime.strptime(expiration_date, "%Y-%m-%d")
        if datetime.now() > expiration_date:
            print("授权码已过期！Err code: 400")
            return False

        # 2. 验证客户名： asr
        # 因为可能通过篡改时间来绕过授权码有效期检查，所以再加一道，
        # 经过编译后，只有管理员知道客户名
        if customer_id != "asr":
            print("授权码错误！Err code: 401")
            return False

        # 3. 验证授权码的签名
        public_key.verify(
            signature,
            auth_data.encode(),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )

        # 完成验证
        return True

    except Exception as e:
        # 解密错误 或者 签名验证错误
        print(f"授权码验证失败！Err code: 402")
        return False

def verify_license():
    # 加载公钥
    public_key_file = "/ws/lic/public_key.pem"
    public_key = load_public_key(public_key_file)

    # 加载授权码
    license_key_file = "/ws/lic/license.key"
    if os.path.exists(license_key_file):
        with open(license_key_file, 'r')as f:
            license_key = f.read().strip()
    else:
        license_key = input("请输入授权码：")
    
    if verify_license_(license_key, public_key):
        with open(license_key_file, "w") as f:
            f.write(license_key)
        print("License check done.")
        return True
    else:
        if os.path.exists(license_key_file):
            os.remove(license_key_file)
        print(f"缺少授权码文件{license_key_file}，请联系服务方提供有效授权码！")
        return False

if __name__ == '__main__':
    verify_license()