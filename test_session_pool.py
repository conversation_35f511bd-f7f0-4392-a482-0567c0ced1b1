#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话池的动态扩容功能
"""

import time
import threading
from modules.onnx_session_pool import ONNXSessionPool

# 模拟ONNX会话创建函数
def mock_session_factory():
    """模拟创建ONNX会话"""
    import uuid
    session_id = str(uuid.uuid4())[:8]
    print(f"创建模拟会话: {session_id}")
    return session_id, ["input1", "input2"], {"model_info": "test"}

def test_concurrent_access():
    """测试并发访问会话池"""
    config = {
        'max_sessions_per_model': 2,  # 设置最大2个会话便于测试
        'session_timeout': 300
    }

    pool = ONNXSessionPool(config)

    # 注册模型
    pool.register_model("test_model", mock_session_factory)

    def worker(worker_id):
        """工作线程函数"""
        print(f"Worker {worker_id} 开始获取会话")
        session_info = pool.get_session("test_model", timeout=10.0)

        if session_info:
            print(f"Worker {worker_id} 获取到会话: {session_info.uid}")
            # 模拟使用会话
            time.sleep(2)
            # 释放会话
            pool.release_session("test_model", session_info)
            print(f"Worker {worker_id} 释放会话: {session_info.uid}")
        else:
            print(f"Worker {worker_id} 获取会话失败")

    def worker_fast_release(worker_id):
        """模拟真实场景：快速获取和释放会话"""
        print(f"FastWorker {worker_id} 开始获取会话")
        session_info = pool.get_session("test_model", timeout=10.0)

        if session_info:
            print(f"FastWorker {worker_id} 获取到会话: {session_info.uid}")
            # 模拟快速推理（类似真实的ONNX推理）
            time.sleep(0.05)  # 50ms，模拟真实推理时间
            # 释放会话
            pool.release_session("test_model", session_info)
            print(f"FastWorker {worker_id} 释放会话: {session_info.uid}")
        else:
            print(f"FastWorker {worker_id} 获取会话失败")

    def worker_simultaneous(worker_id):
        """模拟同时获取会话的场景"""
        print(f"SimWorker {worker_id} 开始获取会话")
        session_info = pool.get_session("test_model", timeout=10.0)

        if session_info:
            print(f"SimWorker {worker_id} 获取到会话: {session_info.uid}")
            # 持续占用会话一段时间
            time.sleep(1)
            pool.release_session("test_model", session_info)
            print(f"SimWorker {worker_id} 释放会话: {session_info.uid}")
        else:
            print(f"SimWorker {worker_id} 获取会话失败")

    print("=== 测试1: 模拟真实场景（快速释放）===")
    threads = []
    for i in range(4):  # 4个线程，快速获取和释放
        thread = threading.Thread(target=worker_fast_release, args=(i,))
        threads.append(thread)
        thread.start()
        time.sleep(0.01)  # 很短的间隔，模拟真实的并发请求

    for thread in threads:
        thread.join()

    print("\n=== 测试2: 模拟真正的并发（同时获取）===")
    threads = []
    for i in range(3):  # 3个线程同时获取会话
        thread = threading.Thread(target=worker_simultaneous, args=(i,))
        threads.append(thread)

    # 同时启动所有线程
    for thread in threads:
        thread.start()

    for thread in threads:
        thread.join()

    # 打印统计信息
    stats = pool.get_pool_stats()
    print(f"\n最终统计: {stats}")

    pool.shutdown()

if __name__ == "__main__":
    test_concurrent_access()
