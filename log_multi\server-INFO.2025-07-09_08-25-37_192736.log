2025-07-09 08:25:38.874 | INFO  | modules.config:load_global_config:260 - 成功加载全局配置: /ws/ministream_v2_refactor/modules/../conf/config.yaml
2025-07-09 08:25:38.874 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/zh.yaml
2025-07-09 08:25:38.877 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/en.yaml
2025-07-09 08:25:38.878 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/ru.yaml
2025-07-09 08:25:38.880 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/kk.yaml
2025-07-09 08:25:38.882 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/kkin.yaml
2025-07-09 08:25:38.884 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/ug.yaml
2025-07-09 08:25:38.886 | INFO  | modules.config:_validate_args:554 - 配置参数验证通过
2025-07-09 08:25:38.947 | INFO  | modules.config:load_global_config:260 - 成功加载全局配置: /ws/ministream_v2_refactor/modules/../conf/config.yaml
2025-07-09 08:25:38.947 | INFO  | modules.config:_validate_args:554 - 配置参数验证通过
2025-07-09 08:25:38.952 | INFO  | modules.config:init_logger :593 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-09 08:25:38.966 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 8081
2025-07-09 08:25:38.966 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-09 08:25:38.967 | INFO  | modules.config:init_monitoring:624 - 监控系统初始化成功
2025-07-09 08:25:38.969 | INFO  | modules.onnx_session_pool:__init__    :60 - ONNX会话池已启用, 每个模型最大会话数: 4
2025-07-09 08:25:38.970 | INFO  | modules.config:init_session_pool:649 - ONNX会话池初始化成功
2025-07-09 08:25:38.970 | INFO  | modules.config:init_all_modules:667 - 所有模块初始化完成
2025-07-09 08:25:38.970 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-09 08:25:38.970 | INFO  | server :lifespan    :88 - 启动多语种ASR服务模式: 预加载所有支持的语种模型, 自动识别语种并切换
2025-07-09 08:25:38.970 | INFO  | modules.asr_manager:load_models :56 - 多语种模式：预加载所有支持的语种模型
2025-07-09 08:25:40.435 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 zh 模型已注册到会话池
2025-07-09 08:25:42.385 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 en 模型已注册到会话池
2025-07-09 08:25:42.818 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 ru 模型已注册到会话池
2025-07-09 08:25:43.346 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 kk 模型已注册到会话池
2025-07-09 08:25:43.773 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 kkin 模型已注册到会话池
2025-07-09 08:25:43.784 | INFO  | modules.symbol_table:__init__    :15 - 加载映射表: /ws/MODELS/online_onnx_kkin/map_kkin2lat.txt
2025-07-09 08:25:44.203 | INFO  | modules.asr_manager:_load_single_language:104 - 语种 ug 模型已注册到会话池
2025-07-09 08:25:44.213 | INFO  | modules.symbol_table:__init__    :15 - 加载映射表: /ws/MODELS/online_onnx_ug/map_uyg2lat.txt
2025-07-09 08:25:44.213 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 6/6 个语种
2025-07-09 08:25:44.282 | INFO  | server :lifespan    :106 - LID管理器初始化成功: /ws/MODELS/lid_model/lid.onnx
2025-07-09 08:25:44.282 | INFO  | server :lifespan    :118 - Server start, init manager, LID_MANAGER, ASR_MANAGER
2025-07-09 08:25:54.306 | INFO  | server :websocket_endpoint:231 - client_id:111 - 开始初始化连接
2025-07-09 08:25:54.308 | INFO  | server :websocket_endpoint:237 - client_id:111 - 连接初始化完成，当前活跃连接数: 1
2025-07-09 08:25:54.309 | INFO  | server :websocket_endpoint:231 - client_id:555 - 开始初始化连接
2025-07-09 08:25:54.311 | INFO  | server :websocket_endpoint:237 - client_id:555 - 连接初始化完成，当前活跃连接数: 2
2025-07-09 08:25:54.489 | INFO  | modules.connect:on_check    :446 - client_id:111 - 设置自定义分隔符: ", "
2025-07-09 08:25:54.490 | INFO  | modules.connect:_handle_language_options:158 - client_id:111 - 客户端指定语种: en, 跳过LID
2025-07-09 08:25:54.490 | INFO  | modules.connect:_init_decoder:134 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-09 08:25:54.507 | INFO  | modules.connect:_init_decoder:141 - client_id:111 - 解码器初始化完成, 分隔符: ", "
2025-07-09 08:25:54.513 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 08:25:54.514 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 08:25:54.515 | INFO  | modules.connect:_switch_asr_model:849 - client_id:111 - 重新创建解码器以使用新语种配置
2025-07-09 08:25:54.515 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:25:54.515 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:25:54.515 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 08:25:54.516 | INFO  | modules.connect:_switch_asr_model:858 - client_id:111 - 成功切换到语种: en
2025-07-09 08:25:54.659 | INFO  | modules.connect:on_check    :446 - client_id:555 - 设置自定义分隔符: ", "
2025-07-09 08:25:54.659 | INFO  | modules.connect:_handle_language_options:158 - client_id:555 - 客户端指定语种: en, 跳过LID
2025-07-09 08:25:54.659 | INFO  | modules.connect:_init_decoder:134 - client_id:555 - 初始化解码器, 使用默认语种: zh
2025-07-09 08:25:54.663 | INFO  | modules.connect:_init_decoder:141 - client_id:555 - 解码器初始化完成, 分隔符: ", "
2025-07-09 08:25:54.670 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-09 08:25:54.672 | INFO  | modules.asr_manager:switch_to_language:199 - 会话池语种 en 已准备就绪
2025-07-09 08:25:54.672 | INFO  | modules.connect:_switch_asr_model:849 - client_id:555 - 重新创建解码器以使用新语种配置
2025-07-09 08:25:54.673 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:25:54.673 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:25:54.674 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 08:25:54.675 | INFO  | modules.connect:_switch_asr_model:858 - client_id:555 - 成功切换到语种: en
2025-07-09 08:25:54.902 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 08:25:55.074 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-09 08:25:55.322 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 08:25:55.324 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.418 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.419 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.420 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.438 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "the"
2025-07-09 08:25:55.475 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-09 08:25:55.476 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.553 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:55.554 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.555 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:55.572 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第0个数据包, 更新识别结果: "scientists"
2025-07-09 08:25:55.720 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 08:25:55.885 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-09 08:25:56.131 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 08:25:56.296 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-09 08:25:56.542 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 08:25:56.544 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.623 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.623 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.624 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.688 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "the surface of the moon is made"
2025-07-09 08:25:56.698 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-09 08:25:56.699 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.773 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:56.774 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.775 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:56.837 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第1个数据包, 更新识别结果: "scientists hope to understand how"
2025-07-09 08:25:56.952 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 08:25:57.112 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-09 08:25:57.361 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 08:25:57.363 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.442 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.443 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.444 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.512 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第2个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust"
2025-07-09 08:25:57.515 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-09 08:25:57.516 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.590 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:57.591 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.592 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:57.659 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第2个数据包, 更新识别结果: "scientists hope to understand how plane form especially how"
2025-07-09 08:25:57.771 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 08:25:57.927 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-09 08:25:58.180 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 08:25:58.337 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-09 08:25:58.590 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 08:25:58.592 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.670 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.671 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.672 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.737 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第3个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the"
2025-07-09 08:25:58.740 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-09 08:25:58.740 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.813 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:58.814 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.815 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:58.878 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第3个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since"
2025-07-09 08:25:58.999 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 08:25:59.153 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-09 08:25:59.409 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 08:25:59.411 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.495 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.497 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.498 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.563 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第4个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the"
2025-07-09 08:25:59.567 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-09 08:25:59.568 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.641 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:25:59.642 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.643 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:25:59.706 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第4个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the"
2025-07-09 08:25:59.816 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 08:25:59.970 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-09 08:26:00.217 | INFO  | modules.connect:on_check    :600 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 534
2025-07-09 08:26:00.219 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.292 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.293 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.293 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.326 | INFO  | modules.decoder:detokenize  :532 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 08:26:00.326 | INFO  | modules.connect:on_decode   :922 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-09 08:26:00.326 | INFO  | modules.connect:on_result   :409 - client_id:111 - <<< [发送] 第5个数据包, 更新识别结果: "the surface of the moon is made of rocks and dust the outer layer of the moon is called the crust"
2025-07-09 08:26:00.327 | INFO  | server :receive     :343 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 08:26:00.327 | INFO  | server :receive     :349 - client_id: 111 - 关闭连接，清理资源
2025-07-09 08:26:00.331 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:26:00.332 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:26:00.332 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
2025-07-09 08:26:00.376 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-09 08:26:00.790 | INFO  | modules.connect:on_check    :600 - client_id:555 - >>> [解析] 第15个数据包, 累计帧数: 586
2025-07-09 08:26:00.792 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.867 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.868 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.868 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.933 | INFO  | modules.decoder:detokenize  :532 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 08:26:00.933 | INFO  | modules.decoder:ort_run     :208 - 获取会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.934 | INFO  | modules.decoder:ort_run     :223 - 释放会话: en_encoder - 2f7e2973
2025-07-09 08:26:00.935 | INFO  | modules.decoder:ort_run     :289 - 获取会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.935 | INFO  | modules.decoder:ort_run     :297 - 释放会话: en_ctc - 8d88b6a5
2025-07-09 08:26:00.968 | INFO  | modules.decoder:detokenize  :532 - 话音检测: 0.75 s 添加分隔符 
2025-07-09 08:26:00.968 | INFO  | modules.connect:on_decode   :922 - client_id:555 - *** 最后一个数据包完成解码 ***
2025-07-09 08:26:00.968 | INFO  | modules.connect:on_result   :409 - client_id:555 - <<< [发送] 第5个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-09 08:26:00.969 | INFO  | server :receive     :343 - client_id:555 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-09 08:26:00.969 | INFO  | server :receive     :349 - client_id: 555 - 关闭连接，清理资源
2025-07-09 08:26:00.970 | INFO  | modules.decoder:__del__     :427 - ASRDecoder 显式释放资源
2025-07-09 08:26:00.970 | INFO  | modules.decoder:__del__     :137 - Encoder 显式释放资源
2025-07-09 08:26:00.971 | INFO  | modules.decoder:__del__     :269 - CTCPrefixBeamSearch 显式释放资源
